import { Metadata } from 'next';
import { DashboardSidebar } from '@/components/dashboard/DashboardSidebar';
import { getUserRoles } from '@/lib/auth/server-auth';

export const metadata: Metadata = {
  title: 'Salon Yönetimi | Sportiva',
  description: 'Sportiva salon yönetim paneli.',
  robots: {
    index: false,
    follow: false,
  },
};

export default async function ManagerGymLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ gymId: string }>;
}) {
  const { gymId } = await params;
  const roles = await getUserRoles();

  // Kullanıcının rolüne göre sidebar mode'unu belirle
  const sidebarMode = roles.includes('company_manager')
    ? 'company_manager'
    : 'gym_manager';

  return (
    <>
      {/* Dynamic Sidebar with gym context */}
      <DashboardSidebar mode={sidebarMode} gymId={gymId} />

      {/* Main Content Area */}
      {children}
    </>
  );
}
