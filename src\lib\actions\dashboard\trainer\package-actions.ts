'use server';

import { GymPackages } from '@/types/database/tables';
import { z } from 'zod';
import { createAction } from '../../core/core';
import { ApiResponse } from '@/types/global/api';

// Package creation schema
const createPackageSchema = z.object({
  gymId: z.uuid('Geçersiz salon ID'),
  name: z.string().min(1, 'Paket adı gereklidir'),
  package_type: z.enum(['appointment_standard', 'appointment_vip', 'daily']),
  duration_days: z.number().int().positive().optional(),
  price: z.number().positive("Fiyat 0'dan büyük olmalıdır"),
  description: z.string().optional(),
  max_participants: z.number().int().positive().optional(),
  session_count: z.number().int().positive().optional(),
  session_duration_minutes: z.number().int().positive().optional(),
  is_active: z.boolean(),
});

type CreatePackageInput = z.infer<typeof createPackageSchema>;

/**
 * Salon paketi oluşturur - Tüm yetkili roller için (RLS ile kontrol)
 */
export async function createGymPackage(
  data: CreatePackageInput
): Promise<ApiResponse<GymPackages>> {
  return await createAction<GymPackages>(
    async (_, supabase) => {
      // Paketi oluştur - RLS politikası ile yetki kontrolü
      const { data: newPackage, error } = await supabase
        .from('gym_packages')
        .insert({
          name: data.name,
          package_type: data.package_type,
          duration_days: data.duration_days || null,
          price: data.price,
          description: data.description || null,
          max_participants: data.max_participants,
          session_count: data.session_count,
          session_duration_minutes: data.session_duration_minutes,
          is_active: data.is_active,
          gym_id: data.gymId,
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Paket oluşturulurken hata: ${error.message}`);
      }

      return newPackage as GymPackages;
    },
    {
      revalidatePaths: [`/dashboard/gym/${data.gymId}/packages`],
    }
  );
}

// Package update schema
const updatePackageSchema = z.object({
  gymId: z.uuid('Geçersiz salon ID'),
  packageId: z.uuid(),
  name: z.string().min(1, 'Paket adı gereklidir').optional(),
  package_type: z.enum(['appointment_standard', 'appointment_vip', 'daily']).optional(),
  duration_days: z.number().int().positive().optional(),
  price: z.number().positive("Fiyat 0'dan büyük olmalıdır").optional(),
  description: z.string().optional(),
  max_participants: z.number().int().positive().optional(),
  session_count: z.number().int().positive().optional(),
  session_duration_minutes: z.number().int().positive().optional(),
  is_active: z.boolean().optional(),
});

type UpdatePackageInput = z.infer<typeof updatePackageSchema>;

/**
 * Salon paketini günceller - Tüm yetkili roller için (RLS ile kontrol)
 */
export async function updateGymPackage(
  data: UpdatePackageInput
): Promise<ApiResponse<GymPackages>> {
  return await createAction<GymPackages>(
    async (_, supabase) => {
      const { packageId, gymId, ...updateData } = data;

      // Paketi güncelle - RLS politikası ile yetki kontrolü
      const { data: updatedPackage, error } = await supabase
        .from('gym_packages')
        .update({
          ...updateData,
          description: updateData.description || null,
          duration_days: updateData.duration_days || null,
        })
        .eq('id', packageId)
        .eq('gym_id', gymId) // Güvenlik için gym_id kontrolü
        .select()
        .single();

      if (error) {
        throw new Error(`Paket güncellenirken hata: ${error.message}`);
      }

      return updatedPackage as GymPackages;
    },
    {
      revalidatePaths: [`/dashboard/gym/${data.gymId}/packages`],
    }
  );
}
