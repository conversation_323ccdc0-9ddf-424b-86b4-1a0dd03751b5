'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';

import { UserPlus } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { InviteTrainerDialog } from '../../../../company/components/InviteTrainerDialog';

export function InviteTrainerButton() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const params = useParams();
  const router = useRouter();
  const gymId = params.gymId as string;

  const handleTrainerInvited = () => {
    // Refresh the page to show updated trainer list
    router.refresh();
  };

  return (
    <>
      <Button onClick={() => setIsDialogOpen(true)}>
        <UserPlus className="mr-2 h-4 w-4" />
        Antrenör Davet Et
      </Button>

      <InviteTrainerDialog
        isOpen={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        onTrainerInvited={handleTrainerInvited}
        gymId={gymId}
      />
    </>
  );
}
