'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Building2, MapPin, Calendar, Plus } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface TrainerGym {
  gym_id: string;
  gym_name: string;
  gym_address: string;
  status: string;
  left_at: string | null;
  created_at: string;
}

interface TrainerGymsClientProps {
  initialGyms: TrainerGym[];
}

export function TrainerGymsClient({ initialGyms }: TrainerGymsClientProps) {
  const [gyms] = useState<TrainerGym[]>(initialGyms);
  const router = useRouter();

  const activeGyms = gyms.filter(gym => gym.status === 'active');
  const inactiveGyms = gyms.filter(gym => gym.status === 'inactive');
  const leftGyms = gyms.filter(gym => gym.status === 'left');

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge
            variant="default"
            className="bg-green-500 text-white dark:bg-green-600"
          >
            Aktif
          </Badge>
        );
      case 'inactive':
        return (
          <Badge
            variant="secondary"
            className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300"
          >
            Pasif
          </Badge>
        );
      case 'left':
        return (
          <Badge
            variant="outline"
            className="border-gray-300 text-gray-600 dark:border-gray-600 dark:text-gray-400"
          >
            Ayrıldı
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="border-gray-300 text-gray-600 dark:border-gray-600 dark:text-gray-400"
          >
            {status}
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleGymClick = (gymId: string, status: string) => {
    if (status === 'active') {
      router.push(`/dashboard/trainer/gym/${gymId}`);
    }
  };

  if (gyms.length === 0) {
    return (
      <div className="py-12 text-center">
        <Building2 className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
        <h3 className="text-foreground mb-2 text-lg font-medium">
          Henüz hiçbir salonda çalışmıyorsunuz
        </h3>
        <p className="text-muted-foreground mb-6">
          Bir salona katılmak için salon yöneticisinden davet bekleyin veya
          salona katılım isteği gönderin.
        </p>
        <Button
          variant="outline"
          className="border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-800"
        >
          <Plus className="mr-2 h-4 w-4" />
          Salon Ara
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Active Gyms */}
      {activeGyms.length > 0 && (
        <div>
          <h2 className="mb-4 text-xl font-semibold">Aktif Salonlar</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {activeGyms.map(gym => (
              <Card
                key={gym.gym_id}
                className="cursor-pointer border-gray-200 bg-white transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-800 dark:hover:shadow-lg"
                onClick={() => handleGymClick(gym.gym_id, gym.status)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      <CardTitle className="text-lg">{gym.gym_name}</CardTitle>
                    </div>
                    {getStatusBadge(gym.status)}
                  </div>
                  {gym.gym_address && (
                    <CardDescription className="flex items-center text-sm">
                      <MapPin className="mr-1 h-4 w-4" />
                      {gym.gym_address}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="text-muted-foreground space-y-2 text-sm">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Katılım: {formatDate(gym.created_at)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Inactive Gyms */}
      {inactiveGyms.length > 0 && (
        <div>
          <h2 className="mb-4 text-xl font-semibold">Pasif Salonlar</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {inactiveGyms.map(gym => (
              <Card key={gym.gym_id} className="opacity-75">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <CardTitle className="text-muted-foreground text-lg">
                        {gym.gym_name}
                      </CardTitle>
                    </div>
                    {getStatusBadge(gym.status)}
                  </div>
                  {gym.gym_address && (
                    <CardDescription className="flex items-center text-sm">
                      <MapPin className="mr-1 h-4 w-4" />
                      {gym.gym_address}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="text-muted-foreground space-y-2 text-sm">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Katılım: {formatDate(gym.created_at)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Left Gyms */}
      {leftGyms.length > 0 && (
        <div>
          <h2 className="mb-4 text-xl font-semibold">Geçmiş Salonlar</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {leftGyms.map(gym => (
              <Card key={gym.gym_id} className="opacity-60">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <Building2 className="text-muted-foreground h-5 w-5" />
                      <CardTitle className="text-muted-foreground text-lg">
                        {gym.gym_name}
                      </CardTitle>
                    </div>
                    {getStatusBadge(gym.status)}
                  </div>
                  {gym.gym_address && (
                    <CardDescription className="flex items-center text-sm">
                      <MapPin className="mr-1 h-4 w-4" />
                      {gym.gym_address}
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="text-muted-foreground space-y-2 text-sm">
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4" />
                      <span>Katılım: {formatDate(gym.created_at)}</span>
                    </div>
                    {gym.left_at && (
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        <span>Ayrılış: {formatDate(gym.left_at)}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
