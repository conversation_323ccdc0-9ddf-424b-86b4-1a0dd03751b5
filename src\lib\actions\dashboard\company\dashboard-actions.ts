'use server';

/**
 * Dashboard Actions - Manager Gym Functions
 *
 * Following Clean Code principles - this file contains only manager gym functions
 * Other dashboard functions are in specialized files:
 * - gym-analytics.ts: Analytics calculations
 * - gym-stats.ts: Basic statistics
 * - manager-overview.ts: Manager overview data
 * - financial-summary.ts: Financial calculations
 * - dashboard-types.ts: Type definitions
 */

import { createAction } from '../../core/core';
import { ApiResponse } from '@/types/global/api';
import { Companies, Gyms } from '@/types/database/tables';

/**
 * Şirketin sahip olduğu tüm salonları getirir
 * Following Clean Code principles - simple data fetching
 */
export async function getManagerGyms(): Promise<ApiResponse<(Gyms & { company?: Companies })[]>> {
  return await createAction<(Gyms & { company?: Companies })[]>(async (_, supabase, userId) => {
    const { data, error } = await supabase
      .from('gyms')
      .select(`
        *,
        company:companies!inner(
          id,
          manager_profile_id,
          logo_url,
          phone,
          email,
          social_links
        )
      `)
      .eq('company.manager_profile_id', userId);

    if (error) {
      throw new Error(error.message);
    }

    return data || [];
  });
}

export async function getManagerGymNames(): Promise<
  ApiResponse<{ id: string; name: string }[]>
> {
  return await createAction<{ id: string; name: string }[]>(
    async (_, supabase, userId) => {
      const { data, error } = await supabase
        .from('gyms')
        .select(`
          id,
          name,
          company:companies!inner(
            id,
            manager_profile_id
          )
        `)
        .eq('company.manager_profile_id', userId);

      if (error) {
        throw new Error(error.message);
      }

      return data || [];
    }
  );
}
