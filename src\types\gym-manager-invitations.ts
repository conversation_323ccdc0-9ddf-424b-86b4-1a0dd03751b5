/**
 * Gym Manager Invitation Types
 * Single-use invite codes for gym manager assignments
 */

import type { GymManagerInvitationStatus } from './database/enums';

export interface GymManagerInvitation {
  id: string;
  company_id: string;
  invite_code: string;
  gym_id: string;
  status: GymManagerInvitationStatus;
  used_by: string | null;
  used_at: string | null;
  expires_at: string;
  created_at: string | null;
  updated_at: string | null;
}

export interface GymManagerInvitationWithDetails extends GymManagerInvitation {
  gym: {
    id: string;
    name: string;
    address: string | null;
  };
  company: {
    id: string;
    name: string;
  };
  used_by_profile?: {
    id: string;
    full_name: string;
    email: string | null;
  };
}

export interface GymManagerInvitationValidation {
  isValid: boolean;
  isExpired: boolean;
  isUsed: boolean;
  invitation?: GymManagerInvitationWithDetails;
  error?: string;
}
