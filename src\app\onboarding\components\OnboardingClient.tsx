'use client';

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Arrow<PERSON><PERSON>, ArrowLeft, Building2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { RoleCard, type RoleOption } from './RoleCard';
import { PersonalInfoForm } from './PersonalInfoForm';
import { MemberForm } from './MemberForm';
import { TrainerForm } from './TrainerForm';
import { PlatformRoles } from '@/types/database/enums';
import { Profiles } from '@/types/database/tables';
import { useOnboardingState } from '@/hooks/useOnboardingState';
import { useOnboardingValidation } from '@/hooks/useOnboardingValidation';
import { useOnboardingSubmit } from '@/hooks/useOnboardingSubmit';
import { StepHeader } from './shared/StepHeader';
import { ErrorAlert } from './shared/ErrorAlert';
import type { PersonalData, MemberData, TrainerData, ManagerData } from '@/types/onboarding';
import { CompanyForm } from './CompanyForm';
import { WelcomeStep } from './WelcomeStep';
import { ReviewStep } from './ReviewStep';
import { OnboardingPaymentStep } from './OnboardingPaymentStep';
import { PlatformPackages } from '@/types/database/tables';
import { useRouter, useSearchParams } from 'next/navigation';
import { CardHeader, CardTitle } from '@/components/ui/card';

/**
 * OnboardingClient - Main orchestrator component
 * Following Clean Code principles - focused on orchestration, logic moved to hooks
 */

/**
 * Role options configuration
 * Clean, type-safe role definitions following DRY principle
 */
const roleOptions: RoleOption[] = [
  {
    id: 'member' as PlatformRoles,
    title: 'Üye',
    description:
      'Salonlara katıl, antrenmanlarını takip et ve hedeflerine ulaş.',
    icon: <Users className="h-8 w-8" />,
    features: [
      'Salon üyelikleri',
      'Antrenman takibi',
      'İlerleme raporları',
      'Sosyal özellikler',
      'Mobil uygulama',
    ],
  },
  {
    id: 'trainer' as PlatformRoles,
    title: 'Antrenör',
    description: 'Müşterilerine rehberlik et, antrenman programları oluştur.',
    icon: <Dumbbell className="h-8 w-8" />,
    features: [
      'Müşteri yönetimi',
      'Program oluşturma',
      'İlerleme takibi',
      'Gelir raporları',
      'Profesyonel profil',
    ],
  },
  {
    id: 'company_manager' as PlatformRoles,
    title: 'Salon Şirketi Oluştur',
    description: 'Salon şirketi kur, birden fazla şube yönet ve büyü.',
    icon: <Crown className="h-8 w-8" />,
    features: [
      'Şirket yönetimi',
      'Çoklu şube işletmeciliği',
      'Merkezi yönetim sistemi',
      'Finansal raporlar',
    ],
  },
];

interface OnboardingClientProps {
  currentRoles: PlatformRoles[];
  userProfile: Profiles | null;
  packages: PlatformPackages[];
}

export function OnboardingClient({ currentRoles, userProfile, packages }: OnboardingClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  // Kullanıcının ad soyad bilgisi var mı kontrol et
  const hasFullName =
    typeof userProfile?.full_name === 'string' &&
    userProfile.full_name.trim().length > 0;

  // Custom hooks for clean separation of concerns
  const {
    step,
    selectedRoles,
    error,
    fieldErrors,
    personalData,
    memberData,
    trainerData,
    managerData,
    handleRoleToggle,
    handleContinueToRoles,
    handleContinueToForms,
    handleBackToRoles,
    handleContinueToReview,
    handleBackToForms,
    handleContinueToPayment,
    updatePersonalData,
    updateMemberData,
    updateTrainerData,
    updateManagerData,
    clearErrors,
    setMainError,
  } = useOnboardingState(currentRoles);

  const {
    validatePersonalData,
    validateMemberData,
    validateTrainerData,
    validateManagerData,
    validateAllForms,
    isFormComplete,
  } = useOnboardingValidation(selectedRoles, hasFullName);

  const { isPending, submitForm } = useOnboardingSubmit(
    selectedRoles,
    hasFullName
  );

  // Eğer query parametreleri ile ödeme adımı isteniyorsa (örn: /onboarding?step=payment)
  // ve kullanıcı manager ise, ödeme adımını doğrudan göster (erken dönüş)
  const stepParam = searchParams.get('step');
  if (stepParam === 'payment' && currentRoles.includes('company_manager')) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
        <div className="mx-auto max-w-5xl">
          <StepHeader
            stepIndex={5}
            totalSteps={5}
            title="Paket Seçimi ve Ödeme"
            description="İşletmenize uygun paketi seçin ve güvenli ödeme ile işlemi tamamlayın."
            progress={100}
          />

          <OnboardingPaymentStep
            packages={packages}
            onBack={() => router.push('/onboarding')}
            onSuccess={() => {
              /* success kartı bileşen içinde gösterilecek */
            }}
          />
        </div>
      </div>
    );
  }

  // Real-time validation handlers using hooks with strict types
  const handlePersonalDataChange = (data: PersonalData) => {
    const error = validatePersonalData(data);
    updatePersonalData(data, error);
  };

  const handleMemberDataChange = (data: MemberData) => {
    const error = validateMemberData(data);
    updateMemberData(data, error);
  };

  const handleTrainerDataChange = (data: TrainerData) => {
    const error = validateTrainerData(data);
    updateTrainerData(data, error);
  };

  const handleManagerDataChange = (data: ManagerData) => {
    const error = validateManagerData(data);
    updateManagerData(data, error);
  };

  // Submit handler using hook
  const handleSubmit = async (): Promise<void> => {
    const validationError = validateAllForms(
      personalData,
      memberData,
      trainerData,
      managerData
    );
    if (validationError) {
      setMainError(validationError);
      return;
    }

    clearErrors();
    const ok = await submitForm(
      personalData,
      memberData,
      trainerData,
      managerData,
      setMainError
    );
    if (ok) {
      if (selectedRoles.includes('company_manager')) {
        handleContinueToPayment();
      } else {
        router.push('/dashboard');
      }
    }
  };

  // Welcome step
  if (step === 'welcome') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
        <div className="mx-auto max-w-3xl">
          <StepHeader
            stepIndex={1}
            totalSteps={5}
            title="Sportiva'ya Hoş Geldiniz"
            description="Rolünüzü seçmeden önce, neler yapabileceğinizi kısaca anlatalım."
            progress={20}
          />
          <WelcomeStep onContinue={handleContinueToRoles} />
        </div>
      </div>
    );
  }

  // Rol seçimi adımı
  if (step === 'roles') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
        <div className="mx-auto max-w-7xl">
          <StepHeader
            stepIndex={2}
            totalSteps={5}
            title="Rolünüzü Seçin"
            description="Sportiva'da hangi ek rolleri almak istiyorsunuz? Mevcut rolleriniz korunacak ve yeni yetenekler kazanacaksınız."
            progress={40}
          />

          {/* Role Cards */}
          <div className="mb-12 grid gap-6 sm:grid-cols-2 lg:grid-cols-3 lg:gap-8">
            {roleOptions.map(role => {
              const isCurrentRole = currentRoles.includes(role.id);
              const canSelectRole = !isCurrentRole;
              const isSelected =
                selectedRoles.includes(role.id) && canSelectRole;
              const isDisabled = !canSelectRole;

              return (
                <RoleCard
                  key={role.id}
                  role={role}
                  isSelected={isSelected}
                  isDisabled={isDisabled}
                  isCurrentRole={isCurrentRole}
                  onClick={() => !isDisabled && handleRoleToggle(role.id)}
                />
              );
            })}
          </div>
          {/* Continue Button */}
          <div className="mt-4 flex justify-center">
            <Button
              onClick={handleContinueToForms}
              disabled={selectedRoles.length === 0}
              size="lg"
              className="px-12 py-4 text-lg font-semibold"
              aria-disabled={selectedRoles.length === 0}
            >
              {selectedRoles.length === 0 ? (
                'Lütfen bir rol seçin'
              ) : (
                <>
                  Devam Et ({selectedRoles.length} rol)
                  <ArrowRight className="ml-2 h-5 w-5" />
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Form adımı
  if (step === 'forms') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
        <div className="mx-auto max-w-4xl">
          <StepHeader
            stepIndex={3}
            totalSteps={5}
            title="Bilgilerinizi Tamamlayın"
            description="Seçtiğiniz roller için gerekli bilgileri doldurun ve Sportiva deneyiminizi kişiselleştirin."
            progress={60}
          />

          <ErrorAlert message={error ?? ''} />

          <div className="space-y-8">
            {/* Personal Info Form - Sadece ad soyad bilgisi yoksa göster */}
            {!hasFullName && (
              <div className="bg-card rounded-lg border p-6">
                <PersonalInfoForm
                  data={personalData}
                  onChange={handlePersonalDataChange}
                />
                {fieldErrors.personal && (
                  <div className="mt-3 text-sm text-red-600 dark:text-red-400">
                    {fieldErrors.personal}
                  </div>
                )}
              </div>
            )}

            {/* Member Form */}
            {selectedRoles.includes('member') && (
              <div className="bg-card rounded-lg border p-6">
                <div className="mb-4 flex items-center">
                  <Users className="text-primary mr-3 h-6 w-6" />
                  <h2 className="text-xl font-semibold">Üye Bilgileri</h2>
                </div>
                <MemberForm
                  data={memberData}
                  onChange={handleMemberDataChange}
                />
                {fieldErrors.member && (
                  <div className="mt-3 text-sm text-red-600 dark:text-red-400">
                    {fieldErrors.member}
                  </div>
                )}
              </div>
            )}

            {/* Trainer Form */}
            {selectedRoles.includes('trainer') && (
              <div className="bg-card rounded-lg border p-6">
                <div className="mb-4 flex items-center">
                  <Dumbbell className="text-primary mr-3 h-6 w-6" />
                  <h2 className="text-xl font-semibold">Antrenör Bilgileri</h2>
                </div>
                <TrainerForm
                  data={trainerData}
                  onChange={handleTrainerDataChange}
                />
                {fieldErrors.trainer && (
                  <div className="mt-3 text-sm text-red-600 dark:text-red-400">
                    {fieldErrors.trainer}
                  </div>
                )}
              </div>
            )}

            {/* Manager Form */}
            {selectedRoles.includes('company_manager') && (
              <div className="bg-card rounded-lg border p-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="text-primary h-5 w-5" />
                    Şirket Bilgileri
                  </CardTitle>
                </CardHeader>
                <CompanyForm
                  data={managerData}
                  onChange={handleManagerDataChange}
                />
                {fieldErrors.manager && (
                  <div className="mt-3 text-sm text-red-600 dark:text-red-400">
                    {fieldErrors.manager}
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-between">
              <Button
                variant="outline"
                onClick={handleBackToRoles}
                size="lg"
                className="px-8"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Geri Dön
              </Button>

              <Button
                onClick={handleContinueToReview}
                disabled={
                  isPending ||
                  !isFormComplete(
                    personalData,
                    memberData,
                    trainerData,
                    managerData
                  )
                }
                size="lg"
                className="px-12"
                aria-disabled={
                  isPending ||
                  !isFormComplete(
                    personalData,
                    memberData,
                    trainerData,
                    managerData
                  )
                }
              >
                Devam Et
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Payment adımı (yalnızca manager)
  if (step === 'payment') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
        <div className="mx-auto max-w-5xl">
          <StepHeader
            stepIndex={5}
            totalSteps={5}
            title="Paket Seçimi ve Ödeme"
            description="İşletmenize uygun paketi seçin ve güvenli ödeme ile işlemi tamamlayın."
            progress={100}
          />

          <OnboardingPaymentStep
            packages={packages}
            onBack={handleContinueToReview}
            onSuccess={() => { /* success kartı bileşen içinde gösterilecek */ }}
          />
        </div>
      </div>
    );
  }

  // Review adımı
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 px-4 py-10 sm:px-6 lg:px-8 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
      <div className="mx-auto max-w-3xl">
        <StepHeader
          stepIndex={4}
          totalSteps={5}
          title="Gözden Geçirin ve Tamamlayın"
          description="Aşağıdaki bilgileri kontrol edin. Gerekirse geri dönüp düzenleyebilirsiniz."
          progress={80}
        />

        <ErrorAlert message={error ?? ''} />

        <ReviewStep
          hasFullName={hasFullName}
          personalData={personalData}
          memberData={memberData}
          trainerData={trainerData}
          managerData={managerData}
          selectedRoles={selectedRoles}
          isSubmitting={isPending}
          onBack={handleBackToForms}
          onSubmit={handleSubmit}
        />
      </div>
    </div>
  );
}
