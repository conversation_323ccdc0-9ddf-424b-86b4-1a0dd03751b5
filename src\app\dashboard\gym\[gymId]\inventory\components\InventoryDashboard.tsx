'use client';

import { useState } from 'react';
import { Plus, Filter, Search, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  InventoryListResponse,
  InventoryCategory,
  InventoryFilters,
  GymInventory
} from '@/types/database/equipment-inventory';
import { InventoryList } from './InventoryList';
import { InventoryFiltersPanel } from './InventoryFiltersPanel';
import { InventoryStatsCards } from './InventoryStatsCards';
import { CreateInventoryDialog } from './CreateInventoryDialog';
import { EditInventoryDialog } from './EditInventoryDialog';
import { StockAdjustmentDialog } from './StockAdjustmentDialog';

interface InventoryDashboardProps {
  gymId: string;
  inventoryData: InventoryListResponse;
  categories: InventoryCategory[];
  initialFilters: InventoryFilters;
}

export function InventoryDashboard({
  gymId,
  inventoryData,
  categories,
  initialFilters,
}: InventoryDashboardProps) {
  const [filters, setFilters] = useState<InventoryFilters>(initialFilters);
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showStockDialog, setShowStockDialog] = useState(false);
  const [selectedInventory, setSelectedInventory] = useState<GymInventory | null>(null);
  const [stockAdjustmentType, setStockAdjustmentType] = useState<'add' | 'remove'>('add');
  const [searchQuery, setSearchQuery] = useState(initialFilters.search || '');

  const { inventory, analytics } = inventoryData;

  const handleFilterChange = (newFilters: InventoryFilters) => {
    setFilters(newFilters);
    // Update URL params
    const params = new URLSearchParams();
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value !== undefined && value !== false) {
        params.set(key, value.toString());
      }
    });
    window.history.replaceState({}, '', `?${params.toString()}`);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    handleFilterChange({ ...filters, search: query });
  };

  const exportInventory = () => {

  };

  const handleEditInventory = (inventory: GymInventory) => {
    setSelectedInventory(inventory);
    setShowEditDialog(true);
  };

  const handleStockAdjustment = (inventory: GymInventory, type: 'add' | 'remove') => {
    setSelectedInventory(inventory);
    setStockAdjustmentType(type);
    setShowStockDialog(true);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Depo</h1>
          <p className="text-muted-foreground">
            Spor salonu deposunu yönetin ve stok takibi yapın
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="gap-2"
          >
            <Filter className="h-4 w-4" />
            Filtreler
          </Button>
          <Button
            variant="outline"
            onClick={exportInventory}
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            Dışa Aktar
          </Button>
          <Button
            onClick={() => setShowCreateDialog(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            Yeni Ürün
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <InventoryStatsCards analytics={analytics} />

      {/* Search and Filters */}
      <div className="space-y-4">
        <div className="flex gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Ürün ara (ad, SKU, açıklama)..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {showFilters && (
          <InventoryFiltersPanel
            filters={filters}
            categories={categories}
            onFiltersChange={handleFilterChange}
          />
        )}
      </div>

      {/* Active Filters */}
      {Object.values(filters).some(value => value !== undefined && value !== false) && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-muted-foreground">Aktif filtreler:</span>
          {filters.category_id && (
            <Badge variant="secondary" className="gap-1">
              Kategori: {categories.find(c => c.id === filters.category_id)?.name}
              <button
                onClick={() => handleFilterChange({ ...filters, category_id: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.status && (
            <Badge variant="secondary" className="gap-1">
              Durum: {filters.status}
              <button
                onClick={() => handleFilterChange({ ...filters, status: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.unit_type && (
            <Badge variant="secondary" className="gap-1">
              Birim: {filters.unit_type}
              <button
                onClick={() => handleFilterChange({ ...filters, unit_type: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.low_stock && (
            <Badge variant="secondary" className="gap-1">
              Düşük Stok
              <button
                onClick={() => handleFilterChange({ ...filters, low_stock: false })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.expired && (
            <Badge variant="secondary" className="gap-1">
              Süresi Geçmiş
              <button
                onClick={() => handleFilterChange({ ...filters, expired: false })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              Arama: {filters.search}
              <button
                onClick={() => handleFilterChange({ ...filters, search: undefined })}
                className="ml-1 hover:text-destructive"
              >
                ×
              </button>
            </Badge>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setFilters({});
              setSearchQuery('');
              window.history.replaceState({}, '', window.location.pathname);
            }}
            className="h-6 px-2 text-xs"
          >
            Tümünü Temizle
          </Button>
        </div>
      )}

      {/* Inventory List */}
      <InventoryList
        inventory={inventory}
        categories={categories}
        onCreateInventory={() => setShowCreateDialog(true)}
        onEditInventory={handleEditInventory}
        onStockAdjustment={handleStockAdjustment}
      />

      {/* Create Inventory Dialog */}
      <CreateInventoryDialog
        open={showCreateDialog}
        onOpenChange={setShowCreateDialog}
        gymId={gymId}
        categories={categories}
      />

      {/* Edit Inventory Dialog */}
      <EditInventoryDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        inventory={selectedInventory}
        categories={categories}
      />

      {/* Stock Adjustment Dialog */}
      <StockAdjustmentDialog
        open={showStockDialog}
        onOpenChange={setShowStockDialog}
        inventory={selectedInventory}
        adjustmentType={stockAdjustmentType}
      />
    </div>
  );
}
