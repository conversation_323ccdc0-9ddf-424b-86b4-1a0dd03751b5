import { MemberAddForm } from '@/components/forms/member-add-form';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';

export default async function AddMemberPage({
  params,
  searchParams,
}: {
  params: Promise<{ gymId: string }>;
  searchParams: Promise<{ error?: string }>;
}) {
  const { gymId } = await params;
  const { error } = await searchParams;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6 border-b pb-6">
        <h1 className="text-3xl font-bold tracking-tight"><PERSON><PERSON></h1>
        <p className="text-muted-foreground">Salon için yeni üye oluşturun</p>
      </div>

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="mt-0.5 h-5 w-5 text-red-600" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-red-800">Hata!</p>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add Member Form */}
      <MemberAddForm
        gymId={gymId}
        variant="company"
        backUrl={`/dashboard/gym/${gymId}/members`}
        successUrl={`/dashboard/gym/${gymId}/members`}
      />
    </div>
  );
}
