import { GymMembersHeader } from './components/gym-members-header';
import { getGymMembers } from '@/lib/actions/all-actions';
import { getGymCapacityUsage } from '@/lib/actions/business/subscription-actions';
import { GymMembersClient } from './components/gym-members-client';
import { CapacityNotice } from '@/components/dashboard/common/capacity-notice';

export default async function GymMembersPage({
  params,
}: {
  params: Promise<{ gymId: string }>;
}) {
  const { gymId } = await params;
  const membersResult = await getGymMembers(gymId);
  const capacityResult = await getGymCapacityUsage(gymId);

  if (!membersResult.success) {
    throw new Error(membersResult.error || 'Üyeler yüklenemedi');
  }

  const members = membersResult.data?.members || [];
  const capacity = capacityResult.success ? capacityResult.data : null;
  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Üyeler</h1>
            <p className="text-muted-foreground">
              Salon üyelerini görüntüleyin ve yönetin
            </p>
            {capacity && (
              <CapacityNotice
                label="Üyeler"
                count={capacity.members.count}
                max={capacity.members.max}
              />
            )}
          </div>
          <GymMembersHeader gymId={gymId} />
        </div>
      </div>
      <div className="space-y-6">
        <GymMembersClient members={members} gymId={gymId} />
      </div>
    </div>
  );
}
