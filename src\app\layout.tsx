import type { ReactNode } from 'react';
import { Oxanium } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import { SettingsInitializer } from '@/components/settings-initializer';
import './globals.css';

const oxanium = Oxanium({ subsets: ['latin'] });
export const metadata = {
  title: {
    default: 'Sportiva | Spor Salonu Yönetim Sistemi',
    template: '%s | Sportiva',
  },
  authors: [{ name: 'Sportiva' }],
  creator: 'Sportiva',
  metadataBase: new URL('https://sportiva.com.tr'),
  openGraph: {
    type: 'website',
    locale: 'tr_TR',
    siteName: 'Sportiva',
  },
  twitter: {
    card: 'summary_large_image',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="tr" suppressHydrationWarning>
      <body className={oxanium.className}>
        <SettingsInitializer />
        <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
          {/* Erişilebilirlik için Skip Link */}
          <a
            href="#main-content"
            className="bg-primary text-primary-foreground sr-only z-50 rounded-md px-4 py-2 focus:not-sr-only focus:absolute focus:top-4 focus:left-4"
          >
            Ana içeriğe geç
          </a>

          {children}
          <Toaster position="bottom-left" />
        </ThemeProvider>
      </body>
    </html>
  );
}
