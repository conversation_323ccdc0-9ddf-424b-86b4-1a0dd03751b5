'use client';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { 
  EquipmentFilters, 
  EquipmentCategory,
  EQUIPMENT_CONDITIONS,
  EQUIPMENT_STATUSES 
} from '@/types/database/equipment-inventory';

interface EquipmentFiltersPanelProps {
  filters: EquipmentFilters;
  categories: EquipmentCategory[];
  onFiltersChange: (filters: EquipmentFilters) => void;
}

export function EquipmentFiltersPanel({
  filters,
  categories,
  onFiltersChange,
}: EquipmentFiltersPanelProps) {
  const handleFilterChange = (key: keyof EquipmentFilters, value: string | undefined) => {
    onFiltersChange({
      ...filters,
      [key]: value === 'all' ? undefined : value || undefined,
    });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Filtreler</CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            disabled={!Object.values(filters).some(Boolean)}
          >
            Temizle
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Category Filter */}
          <div className="space-y-2">
            <Label htmlFor="category">Kategori</Label>
            <Select
              value={filters.category_id || ''}
              onValueChange={(value) => handleFilterChange('category_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Tüm kategoriler" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm kategoriler</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Condition Filter */}
          <div className="space-y-2">
            <Label htmlFor="condition">Durum</Label>
            <Select
              value={filters.condition || ''}
              onValueChange={(value) => handleFilterChange('condition', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Tüm durumlar" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm durumlar</SelectItem>
                {EQUIPMENT_CONDITIONS.map((condition) => (
                  <SelectItem key={condition.value} value={condition.value}>
                    {condition.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Status Filter */}
          <div className="space-y-2">
            <Label htmlFor="status">Statü</Label>
            <Select
              value={filters.status || ''}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Tüm statüler" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tüm statüler</SelectItem>
                {EQUIPMENT_STATUSES.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Location Filter */}
          <div className="space-y-2">
            <Label htmlFor="location">Konum</Label>
            <Input
              id="location"
              placeholder="Konum ara..."
              value={filters.location || ''}
              onChange={(e) => handleFilterChange('location', e.target.value)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
