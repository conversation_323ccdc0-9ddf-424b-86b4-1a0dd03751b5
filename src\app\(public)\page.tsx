import { StructuredData } from '@/components/seo/structured-data';
import { Metadata } from 'next';
import { PricingModern } from '@/components/public/home/<USER>';
import { PremiumHero } from '@/components/premium/premium-hero'
import { PremiumStats } from '@/components/premium/premium-stats'
import { PremiumShowcase } from '@/components/premium/premium-showcase'
import { PremiumTestimonials } from '@/components/premium/premium-testimonials'
import { PremiumCTA } from '@/components/premium/premium-cta'
import { PremiumLogosMarquee } from '@/components/premium/premium-logos-marquee'
import { PremiumROICalculator } from '@/components/premium/premium-roi-calculator'
import { RoleFeatures } from '@/components/public/home/<USER>'
import { AboutFeatures } from '@/components/public/about/about-features'
import { FeaturesGrid } from '@/app/(public)/features/features-grid'
export const metadata: Metadata = {
  title: 'Sportiva | Salon Yönetiminin En İyi Hali',
  description:
    'Kurumsal düzeyde güvenlik, gelişmiş raporlama, çoklu şube yönetimi ve entegrasyonlarla Sportiva ile salonunuzu bir üst seviyeye taşıyın.',
  keywords: [
    'spor salonu yönetimi',
    'fitness yönetim sistemi',
    'salon üye takibi',
    'spor salonu yazılımı',
    'dijital salon yönetimi',
    'fitness teknolojisi',
    'istanbul spor salonu',
    'ankara spor salonu',
    'izmir spor salonu',
    'rize spor salonu',
    'salon bul',
    'fitness merkezi',
  ],
  openGraph: {
    title: 'Sportiva | Salon Yönetiminin En İyi Hali',
    description:
      'Kurumsal düzeyde güvenlik, gelişmiş raporlama, çoklu şube yönetimi ve entegrasyonlarla Sportiva ile salonunuzu bir üst seviyeye taşıyın.',
    images: ['/placeholder.jpg'],
  },
  alternates: {
    canonical: '/',
  },
};

export default function Home() {

  return (
    <>
      <StructuredData
        type="Product"
        data={{
          name: 'Sportiva',
          description:
            'Kurumsal düzeyde güvenlik, gelişmiş raporlama, çoklu şube yönetimi ve entegrasyonlarla Sportiva ile salonunuzu bir üst seviyeye taşıyın.',
          brand: 'Sportiva',
          offers: { priceCurrency: 'TRY', availability: 'InStock' },
        }}
      />
      
      <main className="flex-1">
        <PremiumHero />
        <PremiumStats />
        <PremiumLogosMarquee />
        <AboutFeatures />
        <FeaturesGrid />
        <RoleFeatures />
        <PremiumROICalculator />
        <PremiumShowcase />
        <PremiumTestimonials />
        <section className="py-20">
          <div className="container mx-auto px-4">
            <PricingModern showHeader={true} compact={false} />
          </div>
        </section>
        <PremiumCTA />
      </main>
    </>
  );
}
