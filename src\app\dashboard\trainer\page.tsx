import { Suspense } from 'react';
import { TrainerGymsClient } from './components/trainer-gyms-client';
import { AuthErrorInline } from '@/components/auth/auth-error-handler';
import { getTrainerGyms } from '@/lib/actions/user/trainer-actions';
import {
  DashboardHeader,
  DashboardHeaderSkeleton,
} from '@/components/dashboard/shared/dashboard-header';



// Trainer Gyms Server Component
async function TrainerGyms() {
  try {
    const gymsResponse = await getTrainerGyms();
    const gyms = gymsResponse.success ? gymsResponse.data || [] : [];
    return <TrainerGymsClient initialGyms={gyms} />;
  } catch (error) {
    console.error("Trainer gym'leri getirme hatası:", error);
    return <TrainerGymsClient initialGyms={[]} />;
  }
}

// Error Alert Component - Yeni auth error handler kullanıyor
function ErrorAlert({ error }: { error: string }) {
  return (
    <AuthErrorInline
      error={error === 'access_denied' ? 'gym_access_denied' : 'unknown_error'}
      className="mb-6"
    />
  );
}

// Main Page Component (Server Component)
export default async function TrainerDashboardPage({
  searchParams,
}: {
  searchParams: Promise<{ error?: string }>;
}) {
  const params = await searchParams;

  return (
    <>
      {params.error && <ErrorAlert error={params.error} />}
      {/* Header Section with Streaming */}
      <Suspense fallback={<DashboardHeaderSkeleton mode="trainer" />}>
        <DashboardHeader mode="trainer" />
      </Suspense>

      {/* Gyms Section with Streaming */}
      <Suspense fallback={<div>Salonlar yükleniyor...</div>}>
        <TrainerGyms />
      </Suspense>
    </>
  );
}
