'use client';

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

import { type SidebarMode } from '@/lib/constants';
import { DashboardComponentProps } from '../shared/header-types';
import { getCurrentRole, getCurrentGymId } from '../shared/header-utils';
import { MobileNavigation } from '../mobile-navigation';
import { MobileBreadcrumbs } from '../mobile-breadcrumbs';

/**
 * Dashboard Mobile Navigation
 * - Sadece dashboard sayfalarında kullanılır
 * - Role-based navigation ve gym selection içerir
 * - Breadcrumbs ve sidebar navigation'ı birleştirir
 */
export function DashboardMobileNavigation({
  roleOptions,
  companyGyms,
  trainerGyms,
  isManager,
  isTrainer,
  isMember,
}: DashboardComponentProps) {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  // Calculate role and gym info from pathname
  const currentRole = getCurrentRole(pathname, roleOptions);
  const currentGymId = getCurrentGymId(pathname, currentRole);

  // Sidebar mode'unu belirle
  const getSidebarMode = (): SidebarMode => {
    return currentRole as SidebarMode;
  };

  const handleNavigate = () => {
    setIsOpen(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="md:hidden"
          aria-label="Dashboard navigasyon menüsünü aç"
        >
          <Menu className="h-5 w-5" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-80 p-0">
        <SheetHeader className="sr-only">
          <SheetTitle>Dashboard Navigasyon Menüsü</SheetTitle>
          <SheetDescription>
            Dashboard navigasyonu, rol ve salon seçimi
          </SheetDescription>
        </SheetHeader>

        <div className="flex h-full flex-col">
          {/* Navigation Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Dashboard Navigation Menu */}
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Dashboard Menü</h3>
                <MobileNavigation
                  mode={getSidebarMode()}
                  gymId={currentGymId || undefined}
                  onNavigate={handleNavigate}
                />
              </div>

              {/* Role and Gym Selection */}
              <div className="space-y-4">
                <h3 className="text-muted-foreground text-sm font-medium">
                  Rol ve Salon Seçimi
                </h3>
                <MobileBreadcrumbs
                  roleOptions={roleOptions}
                  companyGyms={companyGyms}
                  trainerGyms={trainerGyms}
                  currentRole={currentRole}
                  currentGymId={currentGymId}
                  isManager={isManager}
                  isTrainer={isTrainer}
                  isMember={isMember}
                />
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
