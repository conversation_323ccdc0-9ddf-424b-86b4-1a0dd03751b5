import Link from 'next/link';
import { ArrowLeft, Upload } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ImportMembersClient } from './components/import-members-client';

export default async function ImportMembersPage({
  params,
}: {
  params: Promise<{ gymId: string }>;
}) {
  const { gymId } = await params;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-2 border-b pb-4">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Toplu Üye İçe Aktar</h1>
            <p className="text-muted-foreground mt-1">
              CSV dosyasından üyeleri içe aktarın, doğ<PERSON>layın ve ekleyin
            </p>
          </div>
          <Button asChild variant="ghost">
            <Link href={`/dashboard/gym/${gymId}/members`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Üyelere Dön
            </Link>
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" /> CSV Yükle ve İçe Aktar
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ImportMembersClient gymId={gymId} />
        </CardContent>
      </Card>
    </div>
  );
}

