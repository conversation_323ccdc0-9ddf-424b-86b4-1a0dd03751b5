import { Profiles } from '@/types';

/**
 * Header Types - Temizlenmiş ve sadece gerekli olanlar
 */

// 🏋️ Role & Gym Types
export interface RoleOption {
  role: string;
  label: string;
  href: string;
  available: boolean;
}

export interface GymData {
  id: string;
  name: string;
  slug: string;
  city: string;
  district: string;
}

// 🔐 Auth Status - Tek source of truth
export interface HeaderAuthStatus {
  authenticated: boolean;
  profile: Profiles | null;
  roleOptions: RoleOption[];
  companyGyms: GymData[];
  trainerGyms: GymData[];
  isManager: boolean;
  isTrainer: boolean;
  isMember: boolean;
}

// 📊 Dashboard Props - Sadeleştirilmiş
export interface DashboardComponentProps {
  roleOptions: RoleOption[];
  companyGyms: GymData[];
  trainerGyms: GymData[];
  isManager: boolean;
  isTrainer: boolean;
  isMember: boolean;
}
