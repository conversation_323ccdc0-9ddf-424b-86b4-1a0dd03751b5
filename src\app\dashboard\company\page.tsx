import { ToastHand<PERSON> } from './components/toast-handler';
import { Suspense } from 'react';
import { DashboardHeader } from '@/components/dashboard/shared/dashboard-header';
import { getManagerCompany, getManagerCompanyBranches } from '@/lib/actions/all-actions';
import { ManagerDashboard } from './components/ManagerDashboard';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default async function ManagerDashboardPage() {
  // TEK ŞİRKET MANTIGI: Manager'ın tek şirketi olmalı
  const companyResponse = await getManagerCompany();

  if (!companyResponse.success) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <h2 className="text-xl font-semibold">Şirket Bilgileri Yüklenemedi</h2>
          <p className="text-muted-foreground">{companyResponse.error}</p>
        </div>
      </div>
    );
  }

  // Şirket yoksa hata göster (artık onboarding'de oluşturulmalı)
  if (!companyResponse.data) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <h2 className="text-xl font-semibold">Şirket Bulunamadı</h2>
          <p className="text-muted-foreground">
            Şirket bilgileriniz bulunamadı. Lütfen destek ekibi ile iletişime geçin.
          </p>
        </div>
        <Link href="/dashboard/company/onboarding" className="mt-4">
          <Button variant="outline">Şirket Oluştur</Button>
        </Link>
      </div>
    );
  }

  const company = companyResponse.data;

  // Şirketin şubelerini getir
  const branchesResponse = await getManagerCompanyBranches();
  const branches = branchesResponse.success ? branchesResponse.data || [] : [];
  return (
    <div className="space-y-6">
      <ToastHandler />
      <DashboardHeader mode='company_manager' />
      <div>
        <Suspense fallback={<div>Loading...</div>}>
          <ManagerDashboard company={company} branches={branches} />
        </Suspense>
      </div>
    </div>
  );
}
