'use client';

import { Companies, Gyms } from '@/types/database/tables';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Settings, 
  MapPin,
  Phone,
  Calendar,
  Users,
  BarChart3,
  Clock,
  Activity
} from 'lucide-react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { getCityNameById } from '@/lib/constants';

interface CompanyDashboardProps {
  company: Companies;
  branches: (Gyms & { company?: any })[];
}

export function ManagerDashboard({ company, branches }: CompanyDashboardProps) {
  const activeBranches = branches.filter((branch) => branch.status === "active");
  const totalCities = new Set(branches.map((branch) => branch.city)).size;

  return (
    <div className="container mx-auto space-y-8 px-4 md:px-6">
      {/* Main Layout Grid */}
      <div className="grid grid-cols-1 gap-8 xl:grid-cols-4">
        {/* Left Column - Company & Gyms */}
        <div className="space-y-8 xl:col-span-3">
          {/* Company Card with Stats */}
          <div
            className={`bg-card/80 relative overflow-hidden rounded-3xl border shadow-sm backdrop-blur-sm ${
              company.status !== 'active' ? 'border-destructive/50' : ''
            }`}
          >
            {/* Status Badge - Top Right */}
            {company.status !== 'active' && (
              <div className="absolute top-4 right-4 z-10">
                <Badge
                  variant="destructive"
                  className="text-xs font-medium shadow-sm"
                >
                  İnaktif
                </Badge>
              </div>
            )}

            {/* Subscription Badge - Top Right (below status if inactive) */}
            {company.subscription_status !== 'active' && (
              <div
                className={`absolute right-4 z-10 ${company.status !== 'active' ? 'top-14' : 'top-4'}`}
              >
                <Badge
                  variant="outline"
                  className="border-amber-500/50 text-xs font-medium text-amber-600 shadow-sm"
                >
                  Abonelik İnaktif
                </Badge>
              </div>
            )}

            {/* accents */}
            <div
              className="absolute -top-20 -right-20 h-64 w-64 rounded-full bg-emerald-500/10 blur-3xl"
              aria-hidden="true"
            />
            <div
              className="absolute -bottom-20 -left-20 h-64 w-64 rounded-full bg-cyan-500/10 blur-3xl"
              aria-hidden="true"
            />
            <div
              className="absolute inset-0 bg-[linear-gradient(rgba(120,120,120,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(120,120,120,0.05)_1px,transparent_1px)] bg-[size:36px_36px]"
              aria-hidden="true"
            />

            <div className="relative p-6 md:p-8">
              {/* Company Header */}
              <div className="mb-8 flex flex-col items-start justify-between gap-6 md:flex-row md:items-center md:gap-8">
                <div className="flex items-start gap-4 md:gap-6">
                  <div className="bg-primary/10 ring-primary/20 flex h-16 w-16 items-center justify-center rounded-2xl ring-1 md:h-20 md:w-20">
                    <Building2 className="text-primary h-8 w-8 md:h-9 md:w-9" />
                  </div>
                  <div>
                    <div className="flex flex-wrap items-center gap-3">
                      <h1 className="text-2xl font-black tracking-tight md:text-3xl lg:text-4xl">
                        {company.name}
                      </h1>
                    </div>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Link href="/dashboard/company/settings">
                    <Button
                      variant="outline"
                      size="sm"
                      aria-label="Şirket ayarlarına git"
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      Ayarlar
                    </Button>
                  </Link>
                </div>
              </div>

              {/* Company Stats */}
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                <div className="bg-background/50 relative overflow-hidden rounded-2xl border p-5">
                  <div
                    className="absolute top-4 right-4 flex size-10 items-center justify-center rounded-xl bg-emerald-500/10"
                    aria-hidden="true"
                  >
                    <Building2 className="h-5 w-5 text-emerald-500" />
                  </div>
                  <div className="text-muted-foreground text-xs">
                    Toplam Şube
                  </div>
                  <div className="mt-2 text-3xl font-bold tracking-tight">
                    {activeBranches.length}
                  </div>
                  <div className="text-muted-foreground mt-1 text-xs">
                    Aktif şube sayısı
                  </div>
                </div>

                <div className="bg-background/50 relative overflow-hidden rounded-2xl border p-5">
                  <div
                    className="absolute top-4 right-4 flex size-10 items-center justify-center rounded-xl bg-cyan-500/10"
                    aria-hidden="true"
                  >
                    <MapPin className="h-5 w-5 text-cyan-500" />
                  </div>
                  <div className="text-muted-foreground text-xs">
                    Şehir Sayısı
                  </div>
                  <div className="mt-2 text-3xl font-bold tracking-tight">
                    {totalCities}
                  </div>
                  <div className="text-muted-foreground mt-1 text-xs">
                    Farklı şehirlerde hizmet
                  </div>
                </div>

                <div className="bg-background/50 relative overflow-hidden rounded-2xl border p-5">
                  <div
                    className="absolute top-4 right-4 flex size-10 items-center justify-center rounded-xl bg-purple-500/10"
                    aria-hidden="true"
                  >
                    <Users className="h-5 w-5 text-purple-500" />
                  </div>
                  <div className="text-muted-foreground text-xs">
                    Toplam Üye
                  </div>
                  <div className="mt-2 text-3xl font-bold tracking-tight">
                    —
                  </div>
                  <div className="text-muted-foreground mt-1 text-xs">
                    Tüm şubelerdeki üyeler
                  </div>
                </div>

                <div className="bg-background/50 relative overflow-hidden rounded-2xl border p-5">
                  <div
                    className="absolute top-4 right-4 flex size-10 items-center justify-center rounded-xl bg-amber-500/10"
                    aria-hidden="true"
                  >
                    <Calendar className="h-5 w-5 text-amber-500" />
                  </div>
                  <div className="text-muted-foreground text-xs">Kuruluş</div>
                  <div className="mt-2 text-3xl font-bold tracking-tight">
                    {new Date(company.created_at!).getFullYear()}
                  </div>
                  <div className="text-muted-foreground mt-1 text-xs">
                    {new Date(company.created_at!).toLocaleDateString('tr-TR', {
                      month: 'long',
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Branches list */}
          <div className="space-y-6">
            <div className="space-y-4">
              {branches.map((gym, index) => (
                <motion.div
                  key={gym.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`group bg-card/80 relative w-full overflow-hidden rounded-2xl border backdrop-blur-sm transition-all duration-300 hover:shadow-lg ${
                    gym.status === 'active'
                      ? 'border-border/50 hover:border-primary/40 hover:shadow-primary/10'
                      : 'border-destructive/50 hover:border-destructive/60'
                  }`}
                >
                  {/* Status Badge - Only show for inactive gyms */}
                  {gym.status !== 'active' && (
                    <div className="absolute top-3 right-3 z-10">
                      <Badge
                        variant="destructive"
                        className="text-xs font-medium shadow-sm"
                      >
                        İnaktif
                      </Badge>
                    </div>
                  )}

                  {/* Card Content */}
                  <Link
                    href={`/dashboard/gym/${gym.id}`}
                    className="block"
                  >
                    <div className="p-6">
                      <div className="flex items-center justify-between gap-6">
                        {/* Left: Header Info */}
                        <div className="flex min-w-0 flex-1 items-center gap-4">
                          <div className="relative flex-shrink-0">
                            <div className="bg-primary/10 ring-primary/20 flex h-12 w-12 items-center justify-center rounded-xl ring-1">
                              <Building2 className="text-primary h-6 w-6" />
                            </div>
                            <div
                              className={`ring-background absolute -right-1 -bottom-1 h-3 w-3 rounded-full ring-2 ${
                                gym.status === 'active'
                                  ? 'bg-green-500'
                                  : 'bg-red-500'
                              }`}
                            />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h3 className="truncate text-lg font-semibold tracking-tight">
                              {gym.name}
                            </h3>
                            <div className="text-muted-foreground mt-1 flex items-center gap-1.5 text-sm">
                              <MapPin className="h-3.5 w-3.5 flex-shrink-0" />
                              <span className="truncate">
                                {gym.district}, {getCityNameById(gym.city!)}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Center: Stats */}
                        <div className="hidden items-center gap-6 md:flex">
                          <div className="text-center">
                            <div className="mb-1 flex items-center gap-2">
                              <Users className="h-4 w-4 text-blue-500" />
                              <span className="text-muted-foreground text-xs">
                                Üyeler
                              </span>
                            </div>
                            <div className="text-lg font-bold">—</div>
                          </div>
                          <div className="text-center">
                            <div className="mb-1 flex items-center gap-2">
                              <BarChart3 className="h-4 w-4 text-green-500" />
                              <span className="text-muted-foreground text-xs">
                                Kapasite
                              </span>
                            </div>
                            <div className="text-lg font-bold">
                              {gym.max_capacity || '—'}
                            </div>
                          </div>
                        </div>

                        {/* Right: Contact Info & Action */}
                        <div className="flex items-center gap-4">
                          <div className="text-muted-foreground hidden text-right text-sm lg:block">
                            {gym.gym_phone && (
                              <div className="mb-1 flex items-center justify-end gap-2">
                                <Phone className="h-3.5 w-3.5" />
                                <span className="truncate">
                                  {gym.gym_phone}
                                </span>
                              </div>
                            )}
                            {gym.opening_time && gym.closing_time && (
                              <div className="flex items-center justify-end gap-2">
                                <Clock className="h-3.5 w-3.5" />
                                <span>
                                  {gym.opening_time.slice(0, 5)} -{' '}
                                  {gym.closing_time.slice(0, 5)}
                                </span>
                              </div>
                            )}
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="group-hover:bg-primary group-hover:text-primary-foreground flex-shrink-0 transition-colors"
                          >
                            <Activity className="mr-2 h-4 w-4" />
                            Dashboard
                          </Button>
                        </div>
                      </div>

                      {/* Mobile Stats - Show on smaller screens */}
                      <div className="border-border/50 mt-4 border-t pt-4 md:hidden">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-background/50 rounded-lg p-3">
                            <div className="mb-1 flex items-center gap-2">
                              <Users className="h-4 w-4 text-blue-500" />
                              <span className="text-muted-foreground text-xs">
                                Üyeler
                              </span>
                            </div>
                            <div className="text-lg font-bold">—</div>
                          </div>
                          <div className="bg-background/50 rounded-lg p-3">
                            <div className="mb-1 flex items-center gap-2">
                              <BarChart3 className="h-4 w-4 text-green-500" />
                              <span className="text-muted-foreground text-xs">
                                Kapasite
                              </span>
                            </div>
                            <div className="text-lg font-bold">—</div>
                          </div>
                        </div>
                        {(gym.gym_phone ||
                          (gym.opening_time && gym.closing_time)) && (
                          <div className="text-muted-foreground mt-3 space-y-2 text-sm">
                            {gym.gym_phone && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-3.5 w-3.5" />
                                <span className="truncate">
                                  {gym.gym_phone}
                                </span>
                              </div>
                            )}
                            {gym.opening_time && gym.closing_time && (
                              <div className="flex items-center gap-2">
                                <Clock className="h-3.5 w-3.5" />
                                <span>
                                  {gym.opening_time.slice(0, 5)} -{' '}
                                  {gym.closing_time.slice(0, 5)}
                                </span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                </motion.div>
              ))}

              {branches.length === 0 && (
                <div className="text-muted-foreground py-8 text-center text-sm">
                  Henüz şube yok. Hemen &quot;Yeni Şube&quot; oluşturun.
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Column - Summary & Upcoming */}
        <div className="space-y-6">
          <div className="bg-card rounded-2xl border p-5">
            <h2 className="text-lg font-semibold tracking-tight">Özet</h2>
            <p className="text-muted-foreground mt-2 text-sm">
              Yönetim panelinin bu alanını kısa bildirimler, duyurular veya
              yakın gelecekte eklenecek özet kartları ile doldurabiliriz.
            </p>
          </div>
          <div className="bg-card rounded-2xl border p-5">
            <h2 className="text-lg font-semibold tracking-tight">Yakında</h2>
            <ul className="text-muted-foreground mt-3 space-y-2 text-sm">
              <li>• Raporlar ve içgörüler</li>
              <li>• Gelir grafikleri</li>
              <li>• Randevu özetleri</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
