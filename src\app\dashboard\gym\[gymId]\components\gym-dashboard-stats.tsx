import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { getGymStats } from '@/lib/actions/dashboard/company/gym-stats';
import { Gyms } from '@/types/database/tables';
import { UserCheck, DollarSign, Package, TrendingUp } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

interface StatsData {
  activeMembers: number;
  totalPackages: number;
  totalRevenue: number;
}

interface GymDashboardStatsProps {
  gym: Gyms;
}

export async function GymDashboardStats({ gym }: GymDashboardStatsProps) {
  const result = await getGymStats(gym.id);
  const stats = (result.data || { activeMembers: 0, totalPackages: 0, totalRevenue: 0 }) as StatsData;

  const statsCards = [
    {
      title: 'Aktif Üyeler',
      value: stats.activeMembers,
      icon: UserCheck,
      color: 'text-chart-1',
      bgColor: 'bg-chart-1/10',
    },
    {
      title: 'Toplam Gelir',
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'text-chart-2',
      bgColor: 'bg-chart-2/10',
    },
    {
      title: 'Aktif Paketler',
      value: stats.totalPackages,
      icon: Package,
      color: 'text-chart-3',
      bgColor: 'bg-chart-3/10',
    },
    {
      title: 'Ortalama Gelir',
      value:
        stats.activeMembers > 0
          ? formatCurrency(stats.totalRevenue / stats.activeMembers)
          : formatCurrency(0),
      icon: TrendingUp,
      color: 'text-chart-4',
      bgColor: 'bg-chart-4/10',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
      {statsCards.map((card, index) => {
        const IconComponent = card.icon;
        return (
          <Card key={index} className="rounded-xl transition-shadow hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-muted-foreground text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className={`rounded-lg p-2 ${card.bgColor}`}>
                <IconComponent className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
