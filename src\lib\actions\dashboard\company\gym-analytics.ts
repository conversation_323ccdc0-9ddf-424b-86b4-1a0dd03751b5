'use server';

import { SupabaseClient } from '@supabase/supabase-js';
import { ApiResponse } from '@/types/global/api';
import { GymAnalyticsData } from './dashboard-types';
import { createAction } from '../../core';

/**
 * Salon analitik verilerini getirir
 * Following Clean Code principles - single responsibility for gym analytics
 */
export async function getGymAnalytics(
  gymId: string
): Promise<ApiResponse<GymAnalyticsData>> {
  return createAction(
    async (_, supabase, _userId) => {
      return await computeGymAnalytics(supabase, gymId);
    }
  );
}

/**
 * Salon analitik verilerini hesaplar
 * Following Clean Code principles - focused calculation logic
 */
async function computeGymAnalytics(
  supabase: SupabaseClient,
  gymId: string
): Promise<GymAnalyticsData> {
  // Paralel veri çekme için Promise.all kullan
  const [
    totalMembersResult,
    activeMembersResult,
    thisMonthRevenueResult,
    lastMonthRevenueResult,
    allRevenueResult,
    activePackagesResult,
    lastMonthMembersResult,
  ] = await Promise.all([
    getTotalMembers(supabase, gymId),
    getActiveMembers(supabase, gymId),
    getThisMonthRevenue(supabase, gymId),
    getLastMonthRevenue(supabase, gymId),
    getAllRevenue(supabase, gymId),
    getActivePackages(supabase, gymId),
    getLastMonthMembers(supabase, gymId),
  ]);

  const totalMembers = totalMembersResult || 0;
  const activeMembers = activeMembersResult || 0;
  const monthlyRevenue = thisMonthRevenueResult || 0;
  const lastMonthRevenueAmount = lastMonthRevenueResult || 0;
  const totalRevenue = allRevenueResult || 0;
  const activePackages = activePackagesResult || 0;
  const lastMonthMembers = lastMonthMembersResult || 0;

  // Büyüme oranları hesapla
  const memberGrowthRate = calculateGrowthRate(totalMembers, lastMonthMembers);
  const revenueGrowthRate = calculateGrowthRate(
    monthlyRevenue,
    lastMonthRevenueAmount
  );

  // Zaman serisi verileri
  const [memberGrowthData, revenueData] = await Promise.all([
    getMemberGrowthData(supabase, gymId),
    getRevenueData(supabase, gymId),
  ]);

  const topPackages = await getTopPackages(supabase, gymId);

  return {
    totalMembers,
    activeMembers,
    monthlyRevenue,
    totalRevenue,
    activePackages,
    memberGrowthRate: Math.round(memberGrowthRate * 100) / 100,
    revenueGrowthRate: Math.round(revenueGrowthRate * 100) / 100,
    memberGrowthData,
    revenueData,
    topPackages,
  };
}

/**
 * Toplam üye sayısını getirir
 */
async function getTotalMembers(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const { count } = await supabase
    .from('gym_memberships')
    .select('*', { count: 'exact', head: true })
    .eq('gym_id', gymId);

  return count || 0;
}

/**
 * Aktif üye sayısını getirir
 */
async function getActiveMembers(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const { count } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        *,
        memberships!inner(gym_id)
      `,
      { count: 'exact', head: true }
    )
    .eq('memberships.gym_id', gymId)
    .eq('status', 'active')
    .gte('end_date', new Date().toISOString());

  return count || 0;
}

/**
 * Bu ayın gelirini getirir
 */
async function getThisMonthRevenue(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const thisMonthStart = getThisMonthStart();

  const { data } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        purchase_price,
        memberships!inner(gym_id)
      `
    )
    .eq('memberships.gym_id', gymId)
    .eq('status', 'active')
    .gte('created_at', thisMonthStart.toISOString());

  return (
    data?.reduce(
      (sum: number, item: any) => sum + (item.purchase_price || 0),
      0
    ) || 0
  );
}

/**
 * Geçen ayın gelirini getirir
 */
async function getLastMonthRevenue(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const thisMonthStart = getThisMonthStart();
  const lastMonthStart = getLastMonthStart();

  const { data } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        purchase_price,
        memberships!inner(gym_id)
      `
    )
    .eq('memberships.gym_id', gymId)
    .eq('status', 'active')
    .gte('created_at', lastMonthStart.toISOString())
    .lt('created_at', thisMonthStart.toISOString());

  return (
    data?.reduce(
      (sum: number, item: any) => sum + (item.purchase_price || 0),
      0
    ) || 0
  );
}

/**
 * Toplam geliri getirir
 */
async function getAllRevenue(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const { data } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        purchase_price,
        memberships!inner(gym_id)
      `
    )
    .eq('memberships.gym_id', gymId)
    .eq('status', 'active');

  return (
    data?.reduce(
      (sum: number, item: any) => sum + (item.purchase_price || 0),
      0
    ) || 0
  );
}

/**
 * Aktif paket sayısını getirir
 */
async function getActivePackages(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const { count } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        *,
        memberships!inner(gym_id)
      `,
      { count: 'exact', head: true }
    )
    .eq('memberships.gym_id', gymId)
    .eq('status', 'active');

  return count || 0;
}

/**
 * Geçen ayın üye sayısını getirir
 */
async function getLastMonthMembers(
  supabase: SupabaseClient,
  gymId: string
): Promise<number> {
  const thisMonthStart = getThisMonthStart();

  const { count } = await supabase
    .from('gym_memberships')
    .select('*', { count: 'exact', head: true })
    .eq('gym_id', gymId)
    .lt('created_at', thisMonthStart.toISOString());

  return count || 0;
}

/**
 * Büyüme oranını hesaplar
 */
function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return 0;
  return ((current - previous) / previous) * 100;
}

/**
 * Bu ay başlangıcını getirir
 */
function getThisMonthStart(): Date {
  const date = new Date();
  date.setDate(1);
  date.setHours(0, 0, 0, 0);
  return date;
}

/**
 * Geçen ay başlangıcını getirir
 */
function getLastMonthStart(): Date {
  const thisMonthStart = getThisMonthStart();
  const lastMonthStart = new Date(thisMonthStart);
  lastMonthStart.setMonth(lastMonthStart.getMonth() - 1);
  return lastMonthStart;
}

/**
 * Üye büyüme verilerini getirir (son 6 ay)
 */
async function getMemberGrowthData(
  supabase: SupabaseClient,
  gymId: string
): Promise<Array<{ month: string; members: number; newMembers: number }>> {
  const data = [];

  for (let i = 5; i >= 0; i--) {
    const monthStart = new Date();
    monthStart.setMonth(monthStart.getMonth() - i);
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);

    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthEnd.getMonth() + 1);

    const [totalMembers, newMembers] = await Promise.all([
      getTotalMembersUntilDate(supabase, gymId, monthEnd),
      getNewMembersInMonth(supabase, gymId, monthStart, monthEnd),
    ]);

    data.push({
      month: monthStart.toLocaleDateString('tr-TR', {
        month: 'short',
        year: 'numeric',
      }),
      members: totalMembers,
      newMembers: newMembers,
    });
  }

  return data;
}

/**
 * Gelir verilerini getirir (son 6 ay)
 */
async function getRevenueData(
  supabase: SupabaseClient,
  gymId: string
): Promise<Array<{ month: string; revenue: number }>> {
  const data = [];

  for (let i = 5; i >= 0; i--) {
    const monthStart = new Date();
    monthStart.setMonth(monthStart.getMonth() - i);
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);

    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthEnd.getMonth() + 1);

    const monthRevenue = await getMonthRevenue(
      supabase,
      gymId,
      monthStart,
      monthEnd
    );

    data.push({
      month: monthStart.toLocaleDateString('tr-TR', {
        month: 'short',
        year: 'numeric',
      }),
      revenue: monthRevenue,
    });
  }

  return data;
}

/**
 * Belirli tarihe kadar toplam üye sayısını getirir
 */
async function getTotalMembersUntilDate(
  supabase: SupabaseClient,
  gymId: string,
  date: Date
): Promise<number> {
  const { count } = await supabase
    .from('gym_memberships')
    .select('*', { count: 'exact', head: true })
    .eq('gym_id', gymId)
    .lt('created_at', date.toISOString());

  return count || 0;
}

/**
 * Belirli ay içinde yeni katılan üye sayısını getirir
 */
async function getNewMembersInMonth(
  supabase: SupabaseClient,
  gymId: string,
  monthStart: Date,
  monthEnd: Date
): Promise<number> {
  const { count } = await supabase
    .from('gym_memberships')
    .select('*', { count: 'exact', head: true })
    .eq('gym_id', gymId)
    .gte('created_at', monthStart.toISOString())
    .lt('created_at', monthEnd.toISOString());

  return count || 0;
}

/**
 * Belirli ay içindeki geliri getirir
 */
async function getMonthRevenue(
  supabase: SupabaseClient,
  gymId: string,
  monthStart: Date,
  monthEnd: Date
): Promise<number> {
  const { data } = await supabase
    .from('gym_membership_packages')
    .select(
      `
        purchase_price,
        memberships!inner(gym_id)
      `
    )
    .eq('memberships.gym_id', gymId)
    .eq('status', 'active')
    .gte('created_at', monthStart.toISOString())
    .lt('created_at', monthEnd.toISOString());

  return (
    data?.reduce(
      (sum: number, item: any) => sum + (item.purchase_price || 0),
      0
    ) || 0
  );
}

/**
 * En çok satan paketleri getirir
 */
async function getTopPackages(
  supabase: SupabaseClient,
  gymId: string
): Promise<Array<{ name: string; sales: number; revenue: number }>> {
  const { data: packageSales } = await supabase
    .from('gym_membership_packages')
    .select(
      `
      purchase_price,
      gym_packages!inner(name),
      memberships!inner(gym_id)
    `
    )
    .eq('memberships.gym_id', gymId)
    .eq('status', 'active');

  const packageStats: { [key: string]: { sales: number; revenue: number } } =
    {};

  packageSales?.forEach((sale: any) => {
    const gymPackage = Array.isArray(sale.gym_packages)
      ? sale.gym_packages[0]
      : sale.gym_packages;
    const packageName = gymPackage?.name || 'Bilinmeyen Paket';
    if (!packageStats[packageName]) {
      packageStats[packageName] = { sales: 0, revenue: 0 };
    }
    packageStats[packageName].sales += 1;
    packageStats[packageName].revenue += sale.purchase_price || 0;
  });

  return Object.entries(packageStats)
    .map(([name, stats]) => ({
      name,
      sales: stats.sales,
      revenue: stats.revenue,
    }))
    .sort((a, b) => b.revenue - a.revenue)
    .slice(0, 5);
}
