import { PricingHero } from '@/components/public/pricing/pricing-hero';
import { PricingComparison } from '@/components/public/pricing/pricing-comparison';
import { PricingFAQ } from '@/components/public/pricing/pricing-faq';
import { PricingCTA } from '@/components/public/pricing/pricing-cta';
import { PricingModern } from '@/components/public/home/<USER>';
import { getAllPlatformPackages } from '@/lib/actions/business/platform-packages';

// ISR: veriyi belirli aralıklarla yenileyerek build-time statik çıktı üretir
export const revalidate = 3600; // 1 saat

export default async function PricingPage() {
  const res = await getAllPlatformPackages();
  const packages = res.data ?? [];

  return (
    <main className="flex-1">
      <PricingHero />
      <PricingModern showHeader={false} compact={false} packages={packages} />
      <PricingComparison packages={packages} />
      <PricingFAQ />
      <PricingCTA />
    </main>
  );
}
