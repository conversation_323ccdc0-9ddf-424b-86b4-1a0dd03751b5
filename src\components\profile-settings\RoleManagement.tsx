'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import {
  Users,
  Crown,
  Dumbbell,
  Plus,
  CheckCircle,
} from 'lucide-react';
import Link from 'next/link';

interface UserRole {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  badge: string;
  badgeVariant: 'default' | 'secondary' | 'destructive' | 'outline';
  isActive: boolean;
  features?: string[];
  color?: string;
}

const roleDefinitions = [
  {
    id: 'member',
    title: 'Üye',
    description: '<PERSON>lara katıl, antrenmanlarını takip et ve hedeflerine ulaş.',
    icon: <Users className="h-5 w-5" />,
    badge: 'Ücretsiz',
    badgeVariant: 'secondary' as const,
    features: ['Salon üyeliği', '<PERSON><PERSON><PERSON>man takibi', '<PERSON><PERSON><PERSON><PERSON>'],
    color: 'bg-blue-500',
  },
  {
    id: 'trainer',
    title: 'Antrenör',
    description: 'Müşterilerine rehberlik et, antrenman programları oluştur.',
    icon: <Dumbbell className="h-5 w-5" />,
    badge: 'Popüler',
    badgeVariant: 'default' as const,
    features: ['Müşteri yönetimi', 'Program oluşturma', 'Gelir takibi'],
    color: 'bg-green-500',
  },
  {
    id: 'company_manager',
    title: 'Şirket Sahibi',
    description: 'Salon şirketi işletmeciliği ve çoklu şube yönetimi.',
    icon: <Crown className="h-5 w-5" />,
    badge: 'Premium',
    badgeVariant: 'destructive' as const,
    features: ['Şirket yönetimi', 'Çoklu şube kontrolü', 'Finansal raporlar'],
    color: 'bg-purple-500',
  },
];

interface RoleManagementProps {
  userRoles: string[];
}

export function RoleManagement({ userRoles }: RoleManagementProps) {
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const processUserRoles = () => {
      try {
        const isMember = userRoles.includes('member');
        const isTrainer = userRoles.includes('trainer');
        const isManager = userRoles.includes('company_manager');

        const processedRoles = roleDefinitions.map(role => ({
          ...role,
          isActive:
            (role.id === 'member' && isMember) ||
            (role.id === 'trainer' && isTrainer) ||
            (role.id === 'company_manager' && isManager),
        }));

        setRoles(processedRoles);
      } catch (error) {
        console.error('Rol kontrolü hatası:', error);
      } finally {
        setIsLoading(false);
      }
    };

    processUserRoles();
  }, [userRoles]);

  if (isLoading) {
    return <div>Roller yükleniyor...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Rol Kartları */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {roles.map(role => (
          <Card
            key={role.id}
            className={`relative transition-all duration-200 ${
              role.isActive
                ? 'ring-2 ring-primary shadow-lg'
                : 'hover:shadow-md'
            }`}
          >
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`rounded-full p-2 ${role.color} text-white`}>
                    {role.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{role.title}</CardTitle>
                    <Badge variant={role.badgeVariant} className="mt-1">
                      {role.badge}
                    </Badge>
                  </div>
                </div>
                {role.isActive && (
                  <CheckCircle className="text-primary h-6 w-6" />
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground text-sm">{role.description}</p>
              
              {/* Özellikler */}
              {role.features && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Özellikler:</h4>
                  <ul className="space-y-1">
                    {role.features.map((feature, index) => (
                      <li key={index} className="text-muted-foreground flex items-center gap-2 text-xs">
                        <div className="bg-primary/20 h-1.5 w-1.5 rounded-full" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Rol Ekleme Butonu */}
              {!role.isActive && (
                <Button asChild variant="outline" size="sm" className="w-full">
                  <Link href="/onboarding">
                    <Plus className="mr-2 h-4 w-4" />
                    {role.id === 'company_manager' ? 'Şirket Oluştur' : 'Rol Ekle'}
                  </Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
