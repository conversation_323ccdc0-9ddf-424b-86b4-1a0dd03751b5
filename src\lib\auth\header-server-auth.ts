'use server';

import { headers } from 'next/headers';
import { getAuthenticatedUser, getUserRoles } from '@/lib/auth/server-auth';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { getManagerGyms } from '@/lib/actions/dashboard/company/dashboard-actions';
import { getTrainerGyms } from '@/lib/actions/user/trainer-actions';
import { createRoleOptions } from '@/components/header/shared/header-constants';
import {
  HeaderAuthStatus,
  GymData,
} from '@/components/header/shared/header-types';

/**
 * Server component'ler için pathname alma
 * Middleware'den gelen x-pathname header'ını okur
 */
export async function getServerPathname(): Promise<string> {
  const headersList = await headers();
  return headersList.get('x-pathname') || '/';
}

/**
 * Server component'ler için header auth durumu
 * Static rendering'i bozmadan auth kontrolü yapar
 */
export async function getServerHeaderAuthStatus(): Promise<HeaderAuthStatus> {
  try {
    // Auth kontrolü yap
    const user = await getAuthenticatedUser();

    if (!user) {
      return {
        authenticated: false,
        profile: null,
        roleOptions: [],
        companyGyms: [],
        trainerGyms: [],
        isManager: false,
        isTrainer: false,
        isMember: false,
      };
    }

    // Paralel veri çekme - performance optimizasyonu
    const [profileResult, roles] = await Promise.all([
      getProfile(),
      getUserRoles(),
    ]);

    // Manager ise gym bilgilerini çek
    let managerGyms: GymData[] = [];
    if (roles.includes('company_manager')) {
      const gymsResult = await getManagerGyms();
      if (gymsResult.success && gymsResult.data) {
        managerGyms = gymsResult.data.map(gym => ({
          id: gym.id,
          name: gym.name,
          slug: gym.slug || '',
          city: gym.city || '',
          district: gym.district || '',
        }));
      }
    }

    // Trainer ise çalıştığı salonları çek
    let trainerGyms: GymData[] = [];
    if (roles.includes('trainer')) {
      const gymsResult = await getTrainerGyms();
      if (gymsResult.success && gymsResult.data) {
        trainerGyms = gymsResult.data
          .filter(gym => gym.status === 'active') // Sadece aktif salonlar
          .map(gym => ({
            id: gym.gym_id,
            name: gym.gym_name,
            slug: '', // Trainer gym'lerinde slug yok, boş bırakıyoruz
            city: gym.gym_city || '',
            district: gym.gym_district || '',
          }));
      }
    }

    return {
      authenticated: true,
      profile:
        profileResult.success && profileResult.data ? profileResult.data : null,
      roleOptions: createRoleOptions(roles),
      companyGyms,
      trainerGyms,
      isManager: roles.includes('company_manager'),
      isTrainer: roles.includes('trainer'),
      isMember: roles.includes('member'),
    };
  } catch (error) {
    console.error('Server header auth status check failed:', error);
    return {
      authenticated: false,
      profile: null,
      roleOptions: [],
      companyGyms: [],
      trainerGyms: [],
      isManager: false,
      isTrainer: false,
      isMember: false,
    };
  }
}
