'use client';

import { 
  Package, 
  AlertTriangle, 
  XCircle, 
  DollarSign,
  Bar<PERSON>hart3
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { InventoryAnalytics } from '@/types/database/equipment-inventory';

interface InventoryStatsCardsProps {
  analytics: InventoryAnalytics;
}

export function InventoryStatsCards({ analytics }: InventoryStatsCardsProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    }).format(amount);
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {/* Total Items */}
      <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-blue-200/50 dark:border-blue-800/50">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-blue-900 dark:text-blue-100">Toplam Ürün</CardTitle>
          <div className="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
            <Package className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{analytics.total_items}</div>
          <div className="flex gap-2 mt-2">
            {analytics.low_stock_items > 0 && (
              <Badge variant="outline" className="text-xs text-yellow-600 dark:text-yellow-400 border-yellow-300 dark:border-yellow-600 bg-yellow-50 dark:bg-yellow-900/30">
                <AlertTriangle className="h-3 w-3 mr-1" />
                {analytics.low_stock_items} Düşük
              </Badge>
            )}
            {analytics.out_of_stock_items > 0 && (
              <Badge variant="destructive" className="text-xs">
                <XCircle className="h-3 w-3 mr-1" />
                {analytics.out_of_stock_items} Tükendi
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Inventory Value */}
      <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-green-200/50 dark:border-green-800/50">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-green-900 dark:text-green-100">Depo Değeri</CardTitle>
          <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
            <DollarSign className="h-4 w-4 text-green-600 dark:text-green-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-900 dark:text-green-100">
            {formatCurrency(analytics.total_inventory_value)}
          </div>
          <p className="text-xs text-green-700/80 dark:text-green-300/80 mt-1">
            Toplam stok değeri
          </p>
        </CardContent>
      </Card>

      {/* Stock Alerts */}
      <Card className="bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 border-amber-200/50 dark:border-amber-800/50">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-amber-900 dark:text-amber-100">Stok Uyarıları</CardTitle>
          <div className="p-2 bg-amber-100 dark:bg-amber-900/50 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-amber-600 dark:text-amber-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {analytics.low_stock_items > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-yellow-600 dark:text-yellow-400">Düşük Stok</span>
                <Badge variant="outline" className="text-xs text-yellow-600 dark:text-yellow-400 border-yellow-300 dark:border-yellow-600 bg-yellow-50 dark:bg-yellow-900/30">
                  {analytics.low_stock_items}
                </Badge>
              </div>
            )}
            {analytics.expired_items > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-red-600 dark:text-red-400">Süresi Geçmiş</span>
                <Badge variant="destructive" className="text-xs">
                  {analytics.expired_items}
                </Badge>
              </div>
            )}
            {analytics.low_stock_items === 0 && analytics.expired_items === 0 && (
              <div className="flex items-center gap-2">
                <Package className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="text-sm text-green-600 dark:text-green-400">Tümü normal</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Transactions */}
      <Card className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/30 dark:to-violet-950/30 border-purple-200/50 dark:border-purple-800/50">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-purple-900 dark:text-purple-100">Aylık İşlemler</CardTitle>
          <div className="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg">
            <BarChart3 className="h-4 w-4 text-purple-600 dark:text-purple-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-green-600 dark:text-green-400">Satın Alma</span>
              <Badge variant="outline" className="text-xs text-green-600 dark:text-green-400 border-green-300 dark:border-green-600 bg-green-50 dark:bg-green-900/30">
                {analytics.monthly_transactions.purchases}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-blue-600 dark:text-blue-400">Satış</span>
              <Badge variant="outline" className="text-xs text-blue-600 dark:text-blue-400 border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/30">
                {analytics.monthly_transactions.sales}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-yellow-600 dark:text-yellow-400">Düzeltme</span>
              <Badge variant="outline" className="text-xs text-yellow-600 dark:text-yellow-400 border-yellow-300 dark:border-yellow-600 bg-yellow-50 dark:bg-yellow-900/30">
                {analytics.monthly_transactions.adjustments}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
