/**
 * Membership business domain types
 *
 * Extracted from membership-related actions and components
 */

import type { GymMemberships, GymPackages } from '../database/tables';

/**
 * Membership package with extended information
 */
export interface MembershipPackageExtended extends GymPackages {
  memberCount?: number;
  isPopular?: boolean;
  features?: string[];
}

/**
 * Membership with member information
 */
export interface MembershipWithMember extends GymMemberships {
  member: {
    id: string;
    created_at: string | null;
    updated_at: string | null;
    email: string | null;
    full_name: string;
    avatar_url: string | null;
    phone_number: string | null;
    is_guest_account: boolean;
  } | null;
}

/**
 * Membership statistics
 */
export interface MembershipStats {
  total: number;
  active: number;
  passive: number;
  suspended: number;
  pending: number;
}

/**
 * Membership package statistics
 */
export interface PackageStats {
  packageId: string;
  packageName: string;
  memberCount: number;
  revenue: number;
  averagePrice: number;
}

/**
 * Membership renewal data
 */
export interface MembershipRenewal {
  membershipId: string;
  packageId: string;
  startDate: string;
  endDate: string;
  price: number;
  paymentMethod: 'cash' | 'card' | 'transfer';
}

/**
 * Membership transfer data
 */
export interface MembershipTransfer {
  fromMembershipId: string;
  toMemberId: string;
  reason: string;
  transferDate: string;
  approvedBy: string;
}
