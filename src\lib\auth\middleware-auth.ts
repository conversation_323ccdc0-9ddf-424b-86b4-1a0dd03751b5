'use server';

import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';
import { env } from '../env';



// Route tanımları
export const AUTH_ROUTES = [
  '/auth/login',
  '/auth/register',
  '/auth',
  '/verify-phone',
  '/auth/verify-email',
  '/auth/forgot-password',
] as const;
export const PROTECTED_ROUTES = [
  '/dashboard',
  '/onboarding',
] as const;

interface AuthMiddlewareResult {
  response: NextResponse;
  user?: any;
  userRoles?: string[];
}

/**
 * Gelişmiş authentication middleware
 * - Session yönetimi
 * - Route protection
 * - Role-based access control
 * - Performance optimizasyonu
 */
export async function authMiddleware(
  request: NextRequest
): Promise<AuthMiddlewareResult> {
  let supabaseResponse = NextResponse.next({ request });

  // Pathname'i header'a ekle (server components için)
  supabaseResponse.headers.set('x-pathname', request.nextUrl.pathname);

  // Supabase client oluştur
  const supabase = createServerClient(
    env.SUPABASE_URL || '',
    env.SUPABASE_ANON_KEY || '',
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({ request });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  // User session'ını al
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();

  const { pathname } = request.nextUrl;

  // Route type'ını belirle
  const isAuthRoute = AUTH_ROUTES.some(route => pathname.startsWith(route));
  const isProtectedRoute = PROTECTED_ROUTES.some(route =>
    pathname.startsWith(route)
  );

  // Auth error durumunda korumalı sayfalardan çıkar
  if (error && isProtectedRoute) {
    const url = request.nextUrl.clone();
    url.pathname = '/error';
    url.searchParams.set('type', 'session_expired');
    url.searchParams.set(
      'message',
      encodeURIComponent(error.message || 'Oturum hatası')
    );
    return { response: NextResponse.redirect(url) };
  }

  // Korumalı sayfalara erişim kontrolü
  if (isProtectedRoute && !user) {
    const url = request.nextUrl.clone();
    url.pathname = '/auth/login';
    url.searchParams.set('redirect', pathname);
    url.searchParams.set(
      'message',
      'Bu sayfaya erişmek için giriş yapmanız gerekiyor'
    );
    return { response: NextResponse.redirect(url) };
  }

  // Giriş yapmış kullanıcıları auth sayfalarından yönlendir
  if (isAuthRoute && user) {
    const url = request.nextUrl.clone();

    // Login sayfasından dashboard'a yönlendir
    if (pathname.startsWith('/auth/login')) {
      // Redirect parametresi varsa oraya yönlendir
      const redirectTo = request.nextUrl.searchParams.get('redirect');
      url.pathname =
        redirectTo && redirectTo.startsWith('/dashboard')
          ? redirectTo
          : '/dashboard';
      url.search = ''; // Query parametrelerini temizle
      return { response: NextResponse.redirect(url) };
    }

    // Register sayfasından onboarding'e yönlendir
    if (pathname.startsWith('/auth/register') || pathname.startsWith('/auth')) {
      url.pathname = '/onboarding';
      return { response: NextResponse.redirect(url) };
    }
  }


  return {
    response: supabaseResponse,
    user,
  };
}


