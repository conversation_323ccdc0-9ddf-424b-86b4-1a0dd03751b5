import { redirect } from 'next/navigation';
import { getUserRoles } from '@/lib/auth/server-auth';

/**
 * Dashboard ana sayfası - kullanıcıyı rolüne göre uygun dashboard'a yönlendirir
 * Bu sayfa hiçbir zaman render edilmez, sadece yönlendirme yapar
 */
export default async function DashboardPage() {
  // Kullanıcının rolüne göre uygun dashboard path'ini al ve yönlendir
  const dashboardPath = async () => {
    try {
      // getUserRoles artık authentication'ı kendi içinde yapıyor
      const roles = await getUserRoles();
      // Öncelik sırası: Manager > Gym Manager > Trainer > Member
      if (roles.includes('company_manager')) return '/dashboard/company';
      if (roles.includes('gym_manager')) return '/dashboard/gym-manager';
      if (roles.includes('trainer')) return '/dashboard/trainer';
      if (roles.includes('member')) return '/dashboard/member';
      return '/onboarding';
    } catch (error) {
      // Auth hatası durumunda login'e y<PERSON>nlendir
      return '/auth/login';
    }
  };
  const path = await dashboardPath();
  redirect(path);
}
