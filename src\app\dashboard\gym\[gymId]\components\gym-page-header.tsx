import { Button } from '@/components/ui/button';
import { Gyms, Companies } from '@/types/database/tables';
import { resolveCityName } from '@/lib/utils/display-helpers';
import { ExternalLink, MapPin, Building2, Clock } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { memo, useMemo } from 'react';
import { StarDisplay } from '@/components/ui/star-rating';

interface GymPageHeaderProps {
  gym: Gyms & { company?: Companies };
}

// Yardımcı fonksiyonlar
const formatLocation = (gym: Gyms): string => {
  const locationParts = [
    gym.district,
    resolveCityName(gym.city) || gym.city,
  ].filter(Boolean);

  return locationParts.join(', ') || 'Konum belirtilmemiş';
};

const formatOperatingHours = (openingTime?: string | null, closingTime?: string | null): string | null => {
  if (!openingTime || !closingTime) return null;

  try {
    const formatTime = (time: string) => {
      const [hours, minutes] = time.split(':');
      return `${hours}:${minutes}`;
    };

    return `${formatTime(openingTime)} - ${formatTime(closingTime)}`;
  } catch {
    return null;
  }
};

const getGymPublicUrl = (gym: Gyms): string => {
  return gym.slug ? `/gym/${gym.slug}` : `/gym/${gym.id}`;
};

export const GymPageHeader = memo(function GymPageHeader({
  gym,
}: GymPageHeaderProps) {
  const location = useMemo(() => formatLocation(gym), [gym]);
  const operatingHours = useMemo(() => formatOperatingHours(gym.opening_time, gym.closing_time), [gym.opening_time, gym.closing_time]);
  const publicUrl = useMemo(() => getGymPublicUrl(gym), [gym]);

  return (
    <div className="relative overflow-hidden rounded-xl shadow-lg h-52 lg:h-80">
      {/* Full Background Image */}
      {gym.cover_image_url ? (
        <Image
          src={gym.cover_image_url}
          alt={`${gym.name} kapak görseli`}
          fill
          className="object-cover transition-transform duration-500 hover:scale-105"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
          priority
        />
      ) : (
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-primary/10 to-primary/5 flex items-center justify-center">
          <Building2 className="h-24 w-24 text-primary/40" />
        </div>
      )}

      {/* Enhanced Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/30" />

      {/* Action Button */}
      <div className="absolute right-4 top-4 z-10">
        <Link
          href={publicUrl}
          target="_blank"
          rel="noopener noreferrer"
          aria-label={`${gym.name} canlı sayfasını görüntüle`}
        >
          <Button
            variant="default"
            size="sm"
            className="gap-2 shadow-lg hover:shadow-xl transition-all duration-200 backdrop-blur-sm"
          >
            <ExternalLink className="h-4 w-4" />
            <span className="hidden sm:inline">Canlıyı Gör</span>
          </Button>
        </Link>
      </div>

      {/* Main Content */}
      <div className="relative z-10 h-full flex flex-col justify-between p-6">
        {/* Top Section - Logo and Name Side by Side */}
        <div className="flex items-start gap-4">
          {/* Company Logo */}
          {gym.company?.logo_url && (
            <div className="h-16 w-16 md:h-20 md:w-20 overflow-hidden rounded-full border-3 border-white/90 shadow-xl backdrop-blur-sm bg-white/10 shrink-0">
              <Image
                src={gym.company.logo_url}
                alt={`${gym.company.name} logosu`}
                width={80}
                height={80}
                className="h-full w-full object-cover"
              />
            </div>
          )}

          {/* Gym Name and Slug */}
          <div className="min-w-0 flex-1 text-white">
            <h1 className="line-clamp-2 text-2xl font-bold leading-tight md:text-3xl lg:text-4xl mb-2">
              {gym.name}
            </h1>

            {/* Slug Badge */}
            {gym.slug && (
              <div className="inline-block rounded-lg bg-black/40 px-3 py-1.5 font-mono text-sm text-white/90 backdrop-blur-sm border border-white/20">
                /{gym.slug}
              </div>
            )}
          </div>
        </div>

        {/* Bottom Section - Details and Rating */}
        <div className="flex flex-col gap-3 text-white md:flex-row md:items-end md:justify-between">
          {/* Left Content - Location and Hours */}
          <div className="min-w-0 flex-1 space-y-2">
            {/* Location */}
            <div className="flex items-center gap-2 text-white/90">
              <MapPin className="h-4 w-4 shrink-0" />
              <span className="truncate text-sm md:text-base">
                {location}
                {gym.address && ` • ${gym.address}`}
              </span>
            </div>

            {/* Operating Hours */}
            {operatingHours && (
              <div className="flex items-center gap-2 text-white/80">
                <Clock className="h-4 w-4 shrink-0" />
                <span className="text-sm md:text-base">
                  {operatingHours}
                </span>
              </div>
            )}
          </div>

          {/* Right Content - Rating */}
          {(gym.average_rating || gym.review_count) && (
            <div className="shrink-0 mt-3 md:mt-0">
              <div className="rounded-lg bg-black/40 p-3 backdrop-blur-sm border border-white/20">
                <StarDisplay
                  rating={Number(gym.average_rating) || 0}
                  totalReviews={Number(gym.review_count) || 0}
                  size="sm"
                  showValue
                  showCount
                  className="text-white [&_.text-muted-foreground]:text-white/80"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
});
