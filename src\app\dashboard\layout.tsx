import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { DashboardHeader } from '@/components/header/dashboard-header';
import { getServerPathname } from '@/lib/auth/header-server-auth';
import {
  getAuthenticatedUser,
  getUserRoles,
  checkManagerActiveSubscription,
  checkManagerGymAccess,
  checkTrainerGymAccess,
} from '@/lib/auth/server-auth';

export const metadata: Metadata = {
  title: 'Dashboard | Sportiva',
  description:
    'Sportiva kullanıcı paneli. Üyeliklerinizi yönetin, salonlarınızı takip edin.',
  robots: {
    index: false,
    follow: false,
  },
};

export const dynamic = 'force-dynamic';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // Read current pathname from header (set by middleware)
  const pathname = await getServerPathname();

  // Ensure authenticated user for any dashboard page
  const user = await getAuthenticatedUser();
  if (!user) {
    const redirectTo = pathname && pathname.startsWith('/dashboard') ? pathname : '/dashboard';
    redirect(`/auth/login?redirect=${encodeURIComponent(redirectTo)}`);
  }

  // Role-based access control within dashboard
  const roles = await getUserRoles();

  // Member area
  if (pathname.startsWith('/dashboard/member')) {
    if (!roles.includes('member')) {
      redirect(
        `/error?type=access_denied&message=${encodeURIComponent('Bu sayfaya erişim yetkiniz bulunmuyor')}`
      );
    }
  }

  // Trainer area (optionally gym-specific)
  if (pathname.startsWith('/dashboard/trainer')) {
    if (!roles.includes('trainer')) {
      redirect(
        `/error?type=access_denied&message=${encodeURIComponent('Bu sayfaya erişim yetkiniz bulunmuyor')}`
      );
    }

    const trainerGymMatch = pathname.match(/\/dashboard\/trainer\/gym\/([^\/]+)/);
    if (trainerGymMatch) {
      const gymId = trainerGymMatch[1];
      const hasAccess = await checkTrainerGymAccess( gymId);
      if (!hasAccess) {
        redirect(
          `/error?type=gym_access_denied&message=${encodeURIComponent('Bu salona erişim yetkiniz bulunmuyor')}`
        );
      }
    }
  }

  // Company (manager) area (requires active subscription, optionally gym-specific)
  if (pathname.startsWith('/dashboard/company')) {
    if (!roles.includes('company_manager') && !roles.includes('gym_manager')) {
      redirect(
        `/error?type=access_denied&message=${encodeURIComponent('Bu sayfaya erişim yetkiniz bulunmuyor')}`
      );
    }

    // Company manager için abonelik kontrolü
    if (roles.includes('company_manager')) {
      const hasActiveSubscription = await checkManagerActiveSubscription();
      if (!hasActiveSubscription) {
        // Aktif aboneliği olmayan manager'ı ödeme adımına yönlendir
        redirect('/onboarding?step=payment');
      }
    }

    const managerGymMatch = pathname.match(/\/dashboard\/company\/gym\/([^\/]+)/);
    if (managerGymMatch) {
      const gymId = managerGymMatch[1];
      const hasGymAccess = await checkManagerGymAccess( gymId);
      if (!hasGymAccess) {
        redirect(
          `/dashboard/company?toast=error&message=${encodeURIComponent('Böyle bir salon mevcut değil')}`
        );
      }
    }
  }

  // Gym Manager area
  if (pathname.startsWith('/dashboard/gym-manager')) {
    if (!roles.includes('gym_manager')) {
      redirect(
        `/error?type=access_denied&message=${encodeURIComponent('Bu sayfaya erişim yetkiniz bulunmuyor')}`
      );
    }
  }

  return (
    <main
      id="main-content"
      className="relative flex min-h-screen w-full flex-col overflow-hidden"
    >
      <DashboardHeader />

      <div className="h-[calc(100vh-4rem)] overflow-y-auto px-4 py-8 md:pr-10 md:pl-24">
        {children}
      </div>
    </main>
  );
}
