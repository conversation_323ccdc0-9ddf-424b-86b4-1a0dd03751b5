import { RoleOption } from './header-types';

// 🎯 Navigation Constants
export const NAV_LINKS = {
  login: '/auth/login',
  register: '/auth/register',
  dashboard: '/dashboard',
  profile: '/profile/settings',
} as const;

// 🏷️ Role Options Constants
export const ROLE_OPTIONS: Record<string, RoleOption> = {
  member: {
    role: 'member',
    label: 'Üye',
    href: '/dashboard/member',
    available: true,
  },
  trainer: {
    role: 'trainer',
    label: 'Antrenö<PERSON>',
    href: '/dashboard/trainer',
    available: true,
  },
  manager: {
    role: 'company_manager',
    label: 'Şirket Paneli',
    href: '/dashboard/company',
    available: true,
  },
};

// 📊 Dashboard Specific Constants
export const DASHBOARD_CONFIG = {
  pathPrefix: '/dashboard',
  roles: {
    member: 'member',
    trainer: 'trainer',
    manager: 'company_manager',
  },
  gymPathPattern: /\/gym\/([^\/]+)/,
} as const;

// 🔧 Utility Functions
export const createRoleOptions = (userRoles: string[]): RoleOption[] => {
  return userRoles
    .filter(role => role in ROLE_OPTIONS)
    .map(role => ROLE_OPTIONS[role]);
};

export const extractCurrentRole = (pathname: string): string => {
  const segments = pathname.split('/');
  const dashboardIndex = segments.indexOf('dashboard');
  return dashboardIndex !== -1 && segments[dashboardIndex + 1]
    ? segments[dashboardIndex + 1]
    : '';
};

export const extractCurrentGymId = (pathname: string): string | null => {
  const segments = pathname.split('/');
  const gymIndex = segments.indexOf('gym');
  return gymIndex !== -1 && segments[gymIndex + 1]
    ? segments[gymIndex + 1]
    : null;
};
