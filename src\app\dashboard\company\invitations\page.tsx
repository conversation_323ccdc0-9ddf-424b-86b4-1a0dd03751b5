import { requireRole } from '@/lib/auth/server-auth';
import { getManagerGyms } from '@/lib/actions/dashboard/company/dashboard-actions';
import { getCompanyInvitations } from '@/lib/actions/dashboard/company/gym-manager-invitation-actions';
import { InvitationsClient } from './components/InvitationsClient';

export const dynamic = 'force-dynamic';

export default async function InvitationsPage() {
  await requireRole('company_manager');

  // Paralel olarak gym'leri ve davet kodlarını getir
  const [gymsResult, invitationsResult] = await Promise.all([
    getManagerGyms(),
    getCompanyInvitations(),
  ]);

  if (!gymsResult.success) {
    throw new Error(gymsResult.error || 'Salonlar yüklenemedi');
  }

  if (!invitationsResult.success) {
    throw new Error(invitationsResult.error || 'Davet kodları yüklenemedi');
  }

  return (
    <InvitationsClient 
      gyms={gymsResult.data || []} 
      invitations={invitationsResult.data || []}
    />
  );
}
