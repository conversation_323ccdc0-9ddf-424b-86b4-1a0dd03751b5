'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Search, Loader2, UserPlus, Users, Mail, QrCode } from 'lucide-react';
import { toast } from 'sonner';
import { findMemberByCode, MemberSearchResult } from '@/lib/actions/gym_invitations/user-search-service';
import { sendGymMemberInvite } from '@/lib/actions/gym_invitations/invitation-actions';

interface InviteMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  gymId: string;
  onMemberInvited?: () => void;
}

export function InviteMemberDialog({
  isOpen,
  onClose,
  gymId,
  onMemberInvited,
}: InviteMemberDialogProps) {
  const [memberCode, setMemberCode] = useState('');
  const [foundMember, setFoundMember] = useState<MemberSearchResult | null>(null);
  const [isSearchingMember, setIsSearchingMember] = useState(false);
  const [invitingUsers, setInvitingUsers] = useState<Set<string>>(new Set());

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setMemberCode('');
      setFoundMember(null);
      setInvitingUsers(new Set());
    }
  }, [isOpen]);

  // Handle member search by code
  const handleMemberSearch = async () => {
    const code = memberCode.trim();
    if (!code) {
      toast.error('Lütfen üye kodunu girin');
      return;
    }

    setIsSearchingMember(true);
    try {
      const result = await findMemberByCode(code);

      if (result.success && result.data) {
        setFoundMember(result.data);
        toast.success('Üye bulundu!');
      } else {
        setFoundMember(null);
        toast.error('Bu kodla üye bulunamadı');
      }
    } catch (error) {
      toast.error('Arama sırasında bir hata oluştu');
      setFoundMember(null);
    } finally {
      setIsSearchingMember(false);
    }
  };

  // Handle invite member by code
  const handleInviteMemberByCode = async () => {
    if (!foundMember) return;

    setInvitingUsers(prev => new Set(prev).add(foundMember.profile_id));

    try {
      const result = await sendGymMemberInvite(gymId, foundMember.profile_id);

      if (result.success) {
        toast.success(`${foundMember.profiles.full_name} başarıyla davet edildi!`);
        onMemberInvited?.();
        setFoundMember(null);
        setMemberCode('');
      } else {
        toast.error(result.error || 'Davet gönderilirken hata oluştu');
      }
    } catch (error) {
      console.error('Invite error:', error);
      toast.error('Davet gönderilirken hata oluştu');
    } finally {
      setInvitingUsers(prev => {
        const newSet = new Set(prev);
        newSet.delete(foundMember.profile_id);
        return newSet;
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Üye Davet Et
          </DialogTitle>
          <DialogDescription>
            Üye kodu ile salon üyesi arayın ve davet edin
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <QrCode className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                <Input
                  placeholder="Üye kodunu girin (örn: **********)"
                  value={memberCode}
                  onChange={e => setMemberCode(e.target.value.toUpperCase())}
                  className="pl-10"
                  onKeyDown={e => e.key === 'Enter' && handleMemberSearch()}
                />
              </div>
              <Button
                onClick={handleMemberSearch}
                disabled={isSearchingMember || !memberCode.trim()}
              >
                {isSearchingMember ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Member Search Result */}
            {foundMember && (
              <Card className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950/50">
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">
                          {foundMember.profiles?.full_name || 'Bilinmeyen Kullanıcı'}
                        </h3>
                        <div className="text-muted-foreground flex items-center gap-2 text-sm">
                          <Mail className="h-3 w-3" />
                          {foundMember.profiles?.email || 'Email bulunamadı'}
                        </div>
                        {foundMember.age && (
                          <div className="text-muted-foreground text-sm">
                            Yaş: {foundMember.age}
                          </div>
                        )}
                      </div>
                      <Badge variant="secondary">
                        {foundMember.invite_code}
                      </Badge>
                    </div>

                    <div className="flex justify-end">
                      <Button
                        onClick={handleInviteMemberByCode}
                        disabled={invitingUsers.has(foundMember.profile_id)}
                      >
                        {invitingUsers.has(foundMember.profile_id) ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Davet Ediliyor...
                          </>
                        ) : (
                          <>
                            <UserPlus className="mr-2 h-4 w-4" />
                            Davet Et
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
