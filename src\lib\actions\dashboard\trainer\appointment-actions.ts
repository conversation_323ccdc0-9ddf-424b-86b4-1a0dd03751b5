'use server';

import { createAction } from '../../core';
import { ApiResponse } from '@/types/global/api';
import { Appointments, AppointmentParticipants, GymMemberships, Profiles, GymMembershipPackages, GymPackages } from '@/types/database/tables';
import { AppointmentStatus } from '@/lib/supabase/types';

// Types for responses
interface TrainerAppointmentWithDetails extends Appointments {
  participants?: (AppointmentParticipants & {
    membership?: GymMemberships & {
      profile?: Profiles;
    };
    membership_package?: GymMembershipPackages & {
      gym_package?: GymPackages;
    };
  })[];
}

/**
 * Get trainer's appointments
 */
export async function getTrainerAppointments(
  trainerId: string,
  filters?: {
    startDate?: string;
    endDate?: string;
    status?: AppointmentStatus;
    gymId?: string;
  }
): Promise<ApiResponse<TrainerAppointmentWithDetails[]>> {
  return createAction<TrainerAppointmentWithDetails[]>(
    async (_, supabase, userId) => {
      try {
        // Ensure trainer can only see their own appointments
        if (trainerId !== userId) {
          throw new Error('Bu randevuları görüntüleme yetkiniz yok');
        }

        // İlk olarak appointments'ları al
        let appointmentsQuery = supabase
          .from('appointments')
          .select('*')
          .eq('trainer_profile_id', trainerId)
          .order('appointment_date', { ascending: true });

        // Apply filters
        if (filters?.startDate) {
          appointmentsQuery = appointmentsQuery.gte('appointment_date', filters.startDate);
        }
        if (filters?.endDate) {
          appointmentsQuery = appointmentsQuery.lte('appointment_date', filters.endDate);
        }
        if (filters?.status) {
          appointmentsQuery = appointmentsQuery.eq('status', filters.status);
        }
        if (filters?.gymId) {
          appointmentsQuery = appointmentsQuery.eq('gym_id', filters.gymId);
        }

        const { data: appointments, error: appointmentsError } = await appointmentsQuery;

        if (appointmentsError) {
          throw new Error(`Randevular getirilemedi: ${appointmentsError.message}`);
        }

        if (!appointments || appointments.length === 0) {
          return [];
        }

        // Ayrı olarak participants'ları getir
        const appointmentIds = appointments.map(a => a.id);
        const { data: participants, error: participantsError } = await supabase
          .from('appointment_participants')
          .select(`
            *,
            membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
              *,
              membership:gym_memberships!gym_membership_packages_membership_id_fkey(
                *,
                profile:profiles!gym_memberships_profile_id_fkey(
                  id,
                  full_name,
                  avatar_url
                )
              ),
              gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
                name,
                package_type,
                session_duration_minutes
              )
            )
          `)
          .in('appointment_id', appointmentIds);

        if (participantsError) {
          console.warn('Katılımcılar getirilemedi:', participantsError.message);
          // Katılımcılar getirilemezse sadece randevuları döndür
          return appointments.map(appointment => ({
            ...appointment,
            participants: []
          })) as TrainerAppointmentWithDetails[];
        }

        // Participants'ları appointment'lara grupla
        const participantsByAppointment = (participants || []).reduce((acc, participant) => {
          const appointmentId = participant.appointment_id;
          if (!acc[appointmentId]) {
            acc[appointmentId] = [];
          }
          acc[appointmentId].push(participant);
          return acc;
        }, {} as Record<string, any[]>);

        // Combine appointments with their participants
        const result = appointments.map(appointment => ({
          ...appointment,
          participants: participantsByAppointment[appointment.id] || []
        })) as TrainerAppointmentWithDetails[];

        return result;
      } catch (error) {
        console.error('getTrainerAppointments error:', error);
        throw new Error(`Randevular yüklenirken hata oluştu: ${error instanceof Error ? error.message : 'Bilinmeyen hata'}`);
      }
    },
  );
}

/**
 * Get trainer's upcoming appointments (next 7 days)
 */
export async function getTrainerUpcomingAppointments(
  trainerId: string
): Promise<ApiResponse<TrainerAppointmentWithDetails[]>> {
  return createAction<TrainerAppointmentWithDetails[]>(
    async (_, supabase, userId) => {
      // Ensure trainer can only see their own appointments
      if (trainerId !== userId) {
        throw new Error('Bu randevuları görüntüleme yetkiniz yok');
      }

      const now = new Date();
      const nextWeek = new Date();
      nextWeek.setDate(now.getDate() + 7);

      const { data, error } = await supabase
        .from('appointments')
        .select(`
          *,
          participants:appointment_participants(
            *,
            membership_package:gym_membership_packages!appointment_participants_gym_membership_package_id_fkey(
              *,
              membership:gym_memberships!gym_membership_packages_membership_id_fkey(
                *,
                profile:profiles!gym_memberships_profile_id_fkey(
                  id,
                  full_name,
                  avatar_url
                )
              ),
              gym_package:gym_packages!gym_membership_packages_gym_package_id_fkey(
                name,
                package_type,
                session_duration_minutes
              )
            )
          )
        `)
        .eq('trainer_profile_id', trainerId)
        .eq('status', 'scheduled')
        .gte('appointment_date', now.toISOString())
        .lte('appointment_date', nextWeek.toISOString())
        .order('appointment_date', { ascending: true });

      if (error) {
        throw new Error(`Yaklaşan randevular getirilemedi: ${error.message}`);
      }

      return data as TrainerAppointmentWithDetails[];
    },
    { requireAuth: true }
  );
}

/**
 * Get trainer's appointment statistics
 */
export async function getTrainerAppointmentStats(
  trainerId: string,
  period: 'week' | 'month' | 'year' = 'month'
): Promise<ApiResponse<{
  total: number;
  completed: number;
  cancelled: number;
  no_show: number;
  upcoming: number;
}>> {
  return createAction<{
    total: number;
    completed: number;
    cancelled: number;
    no_show: number;
    upcoming: number;
  }>(
    async (_, supabase, userId) => {
      // Ensure trainer can only see their own stats
      if (trainerId !== userId) {
        throw new Error('Bu istatistikleri görüntüleme yetkiniz yok');
      }

      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'week':
          startDate = new Date();
          startDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          startDate = new Date();
          startDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          startDate = new Date();
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      const { data, error } = await supabase
        .from('appointments')
        .select('status, appointment_date')
        .eq('trainer_profile_id', trainerId)
        .gte('appointment_date', startDate.toISOString());

      if (error) {
        throw new Error(`İstatistikler getirilemedi: ${error.message}`);
      }

      const stats = {
        total: data.length,
        completed: 0,
        cancelled: 0,
        no_show: 0,
        upcoming: 0,
      };

      data.forEach(appointment => {
        if (appointment.status === 'completed') {
          stats.completed++;
        } else if (appointment.status === 'cancelled') {
          stats.cancelled++;
        } else if (appointment.status === 'no_show') {
          stats.no_show++;
        } else if (appointment.status === 'scheduled' && new Date(appointment.appointment_date) > now) {
          stats.upcoming++;
        }
      });

      return stats;
    },
    { requireAuth: true }
  );
}