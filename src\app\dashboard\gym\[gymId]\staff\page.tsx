import { Suspense } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Users } from 'lucide-react';
import { StaffList } from './components/staff-list';
import { StaffStats } from './components/staff-stats';
import { getStaffByGymId } from '@/lib/actions/all-actions';

interface StaffPageProps {
  params: Promise<{ gymId: string }>;
}

export default async function StaffPage({ params }: StaffPageProps) {
  const { gymId } = await params;

  const response = await getStaffByGymId(gymId);

  const staffs = response.success ? response.data || [] : [];

  return (
    <div className="space-y-6">
      {/* Stats */}
      <StaffStats staffs={staffs} />
      {/* Staff List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Personel Listesi
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<div>Personel listesi yükleniyor...</div>}>
            <StaffList staffs={staffs} gymId={gymId} />
          </Suspense>
        </CardContent>
      </Card>
    </div>
  );
}
