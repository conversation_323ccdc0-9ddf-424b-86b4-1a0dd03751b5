'use client';

import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { GymPackageForm } from '../../../../company/components/gym-package-form';

interface AddPackageButtonProps {
  gymId: string;
  variant?:
    | 'default'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
    | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

export function AddPackageButton({
  gymId,
  variant = 'default',
  size = 'default',
  className,
  children,
}: AddPackageButtonProps) {
  return (
    <GymPackageForm
      gymId={gymId}
      trigger={
        <Button variant={variant} size={size} className={className}>
          <Plus className="mr-2 h-4 w-4" />
          {children || 'Yeni Paket Ekle'}
        </Button>
      }
    />
  );
}
