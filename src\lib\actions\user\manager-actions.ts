'use server';

import { createAction } from '../core';
import { ManagerStatusData } from './types';
import { ManagerSubWithPackageDetails } from './profile-actions';
import { ApiResponse } from '@/types/global/api';

/**
 * Manager aboneliğini iptal eder
 * @param userId - Kullanıcı ID'si
 * @returns boolean - İşlem başarılı ise true
 */
export async function cancelManagerSubscription(): Promise<
  ApiResponse<{ success: boolean }>
> {
  return createAction<{ success: boolean }>(async (_, supabase, authUserId) => {
    const { error } = await supabase
      .from('companies')
      .update({
        subscription_status: 'cancelled',
        updated_at: new Date().toISOString(),
      })
      .eq('manager_profile_id', authUserId);

    if (error) {
      throw new Error(`Manager abonelik iptal hatası: ${error.message}`);
    }

    return { success: true };
  });
}

/**
 * Manager status bilgilerini getirir
 */
export async function getManagerStatus(): Promise<
  ApiResponse<ManagerStatusData>
> {
  return createAction<ManagerStatusData>(async (_, supabase, userId) => {
    // Company bilgilerini platform package ile birlikte al
    const { data: companyData, error: companyError } = await supabase
      .from('companies')
      .select(
        `
          platform_package_id,
          subscription_start_date,
          subscription_end_date,
          subscription_status,
          platform_packages!inner(
            name,
            tier,
            duration
          )
        `
      )
      .eq('manager_profile_id', userId)
      .single();

    // Manager değilse
    if (companyError || !companyData) {
      return {
        isManager: false,
      };
    }

    // Kalan gün hesapla
    let daysRemaining = 0;
    if (companyData.subscription_end_date) {
      const endDate = new Date(companyData.subscription_end_date);
      const today = new Date();
      const diffTime = endDate.getTime() - today.getTime();
      daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }

    // Platform packages join sonucu tek nesne olarak gelir
    const packageData = companyData.platform_packages as any;

    return {
      isManager: true,
      tier: packageData?.tier,
      packageType: packageData?.duration,
      subscriptionStartDate: companyData.subscription_start_date,
      subscriptionEndDate: companyData.subscription_end_date,
      status: companyData.subscription_status,
      daysRemaining: Math.max(0, daysRemaining),
    };
  });
}

/**
 * Kullanıcının manager details bilgilerini getirir
 */
export async function getManagerDetail(): Promise<
  ApiResponse<ManagerSubWithPackageDetails>
> {
  return createAction<ManagerSubWithPackageDetails>(
    async (_, supabase, userId) => {
      // Company bilgilerini platform package ile birlikte al
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select(
          `
          *,
          platform_packages!inner(
            id,
            name,
            tier,
            duration,
            price,
            max_gyms,
            max_members,
            features,
            is_active
          )
        `
        )
        .eq('manager_profile_id', userId)
        .eq('subscription_status', 'active')
        .single();

      if (companyError) {
        // PGRST116 = no rows returned (normal durum - henüz company bilgisi yok)
        if (companyError.code === 'PGRST116') {
          return {
            company: {
              id: '',
              manager_profile_id: '',
              name: '',
              logo_url: null,
              phone: null,
              email: null,
              status: null,
              created_at: null,
              updated_at: null,
              subscription_start_date: null,
              subscription_end_date: null,
              subscription_status: null,
              platform_package_id: null,
              social_links: null,
            },
            package: {
              id: '',
              name: '',
              tier: '',
              duration: '',
              price: 0,
              max_gyms: null,
              max_members: null,
              features: null,
              is_active: null,
            },
          };
        }
        throw new Error(
          'Şirket bilgileri alınamadı: ' + companyError.message
        );
      }

      const packageData = companyData.platform_packages as any;

      return {
        company: companyData,
        package: {
          id: packageData?.id || '',
          name: packageData?.name || '',
          tier: packageData?.tier || '',
          duration: packageData?.duration || '',
          price: packageData?.price || 0,
          max_gyms: packageData?.max_gyms || null,
          max_members: packageData?.max_members || null,
          features: packageData?.features || null,
          is_active: packageData?.is_active || null,
        },
      };
    }
  );
}

/**
 * Manager kaydı oluşturur (platform package satın alma)
 * @param platformPackageId - Platform paket ID'si
 * @returns İşlem sonucu
 */
export async function createManager(
  platformPackageId: string
): Promise<ApiResponse<{ success: boolean; message: string }>> {
  return createAction<{ success: boolean; message: string }>(
    async (_, supabase, userId) => {
      if (!platformPackageId) {
        throw new Error('Platform paket ID gereklidir.');
      }

      // Önce platform paket bilgilerini al
      const { data: packageData, error: packageError } = await supabase
        .from('platform_packages')
        .select('*')
        .eq('id', platformPackageId)
        .eq('is_active', true)
        .single();

      if (packageError) {
        throw new Error(`Platform paketi bulunamadı: ${packageError.message}`);
      }

      if (!packageData) {
        throw new Error('Platform paketi bulunamadı.');
      }

      // Kullanıcının zaten aktif company'si var mı kontrol et
      const { data: existingCompany, error: existingError } =
        await supabase
          .from('companies')
          .select('id')
          .eq('manager_profile_id', userId)
          .eq('subscription_status', 'active')
          .maybeSingle();

      if (existingError && existingError.code !== 'PGRST116') {
        throw new Error(
          `Mevcut abonelik kontrolü yapılamadı: ${existingError.message}`
        );
      }

      if (existingCompany) {
        throw new Error('Zaten aktif bir manager aboneliğiniz bulunmaktadır.');
      }

      // Subscription tarihlerini hesapla
      const startDate = new Date();
      const endDate = new Date();

      // Duration'a göre bitiş tarihini hesapla
      if (packageData.duration === 'monthly') {
        endDate.setMonth(endDate.getMonth() + 1);
      } else if (packageData.duration === 'yearly') {
        endDate.setFullYear(endDate.getFullYear() + 1);
      } else {
        throw new Error('Geçersiz paket süresi.');
      }

      // Kullanıcının company'sini bul ve subscription bilgilerini güncelle
      const { data: userCompany, error: companyFindError } = await supabase
        .from('companies')
        .select('id, subscription_status')
        .eq('manager_profile_id', userId)
        .single();

      if (companyFindError) {
        throw new Error(
          `Şirket bulunamadı. Önce şirket oluşturmanız gerekiyor: ${companyFindError.message}`
        );
      }

      // Eğer company zaten active ise hata ver
      if (userCompany.subscription_status === 'active') {
        throw new Error('Zaten aktif bir manager aboneliğiniz bulunmaktadır.');
      }

      // Company'nin subscription bilgilerini güncelle
      const { error: updateError } = await supabase
        .from('companies')
        .update({
          platform_package_id: platformPackageId,
          subscription_start_date: startDate.toISOString(),
          subscription_end_date: endDate.toISOString(),
          subscription_status: 'active',
          updated_at: new Date().toISOString(),
        })
        .eq('id', userCompany.id);

      if (updateError) {
        throw new Error(
          `Abonelik bilgileri güncellenemedi: ${updateError.message}`
        );
      }

      return {
        success: true,
        message: `${packageData.name} paketine başarıyla abone oldunuz! Artık salon yöneticisi olarak sistemi kullanabilirsiniz.`,
      };
    }
  );
}
