-- Gym Manager Invitations Table for Single-Use Invite Codes
-- 60 second expiry, single use, secure invite system
-- Only for gym manager role - trainers have existing system, staff doesn't need invites

CREATE TABLE IF NOT EXISTS gym_manager_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  -- Invite code system
  invite_code VARCHAR(8) NOT NULL UNIQUE, -- 8 karakter ben<PERSON> kod

  -- Gym assignment (required for gym manager)
  gym_id UUID NOT NULL REFERENCES gyms(id) ON DELETE CASCADE, -- Hangi salon için yönetici

  -- Security and tracking
  status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'cancelled')),
  used_by UUID REFERENCES profiles(id) ON DELETE SET NULL, -- <PERSON>
  used_at TIMESTAMP WITH TIME ZONE, -- <PERSON><PERSON> <PERSON> kullanıldı

  -- Timing - 24 saat geçerlilik süresi
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_gym_manager_invitations_code ON gym_manager_invitations(invite_code);
CREATE INDEX idx_gym_manager_invitations_company ON gym_manager_invitations(company_id);
CREATE INDEX idx_gym_manager_invitations_gym ON gym_manager_invitations(gym_id);
CREATE INDEX idx_gym_manager_invitations_status ON gym_manager_invitations(status);
CREATE INDEX idx_gym_manager_invitations_expires ON gym_manager_invitations(expires_at);

-- RLS Policies
ALTER TABLE gym_manager_invitations ENABLE ROW LEVEL SECURITY;

-- Company managers can see their gym manager invitations
CREATE POLICY "Company managers can view gym manager invitations" ON gym_manager_invitations
  FOR SELECT USING (
    company_id IN (
      SELECT id FROM companies WHERE manager_profile_id = auth.uid()
    )
  );

-- Company managers can create gym manager invitations
CREATE POLICY "Company managers can create gym manager invitations" ON gym_manager_invitations
  FOR INSERT WITH CHECK (
    company_id IN (
      SELECT id FROM companies WHERE manager_profile_id = auth.uid()
    )
    AND gym_id IN (
      SELECT id FROM gyms WHERE company_id IN (
        SELECT id FROM companies WHERE manager_profile_id = auth.uid()
      )
    )
  );

-- Company managers can update their gym manager invitations (cancel, etc.)
CREATE POLICY "Company managers can update gym manager invitations" ON gym_manager_invitations
  FOR UPDATE USING (
    company_id IN (
      SELECT id FROM companies WHERE manager_profile_id = auth.uid()
    )
  );

-- Anyone can read pending gym manager invitations by code (for joining)
CREATE POLICY "Anyone can read pending gym manager invitations by code" ON gym_manager_invitations
  FOR SELECT USING (
    status = 'pending'
    AND expires_at > NOW()
  );

-- Function to generate unique gym manager invite codes
CREATE OR REPLACE FUNCTION generate_gym_manager_invite_code()
RETURNS VARCHAR(8) AS $$
DECLARE
  chars VARCHAR(36) := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  result VARCHAR(8) := '';
  i INTEGER;
  code_exists BOOLEAN;
BEGIN
  LOOP
    result := '';
    -- Generate 8 character code
    FOR i IN 1..8 LOOP
      result := result || substr(chars, floor(random() * length(chars) + 1)::INTEGER, 1);
    END LOOP;

    -- Check if code already exists
    SELECT EXISTS(SELECT 1 FROM gym_manager_invitations WHERE invite_code = result) INTO code_exists;

    -- If unique, break the loop
    IF NOT code_exists THEN
      EXIT;
    END IF;
  END LOOP;

  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to auto-expire old gym manager invitations
CREATE OR REPLACE FUNCTION expire_old_gym_manager_invitations()
RETURNS VOID AS $$
BEGIN
  UPDATE gym_manager_invitations
  SET status = 'expired', updated_at = NOW()
  WHERE status = 'pending'
    AND expires_at <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate gym manager invite codes
CREATE OR REPLACE FUNCTION set_gym_manager_invite_code()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.invite_code IS NULL OR NEW.invite_code = '' THEN
    NEW.invite_code := generate_gym_manager_invite_code();
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_gym_manager_invite_code
  BEFORE INSERT ON gym_manager_invitations
  FOR EACH ROW
  EXECUTE FUNCTION set_gym_manager_invite_code();

-- Trigger to update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_gym_manager_invitations_updated_at
  BEFORE UPDATE ON gym_manager_invitations
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add gym manager support to gyms table
ALTER TABLE gyms ADD COLUMN IF NOT EXISTS manager_profile_id UUID REFERENCES profiles(id) ON DELETE SET NULL;

-- Index for gym manager lookups
CREATE INDEX IF NOT EXISTS idx_gyms_manager ON gyms(manager_profile_id);

-- Update RLS policy for gyms to include gym managers
DROP POLICY IF EXISTS "Managers can manage their gyms" ON gyms;
CREATE POLICY "Managers can manage their gyms" ON gyms
  FOR ALL USING (
    -- Company manager access
    company_id IN (
      SELECT id FROM companies WHERE manager_profile_id = auth.uid()
    )
    OR
    -- Gym manager access
    manager_profile_id = auth.uid()
  );

-- Comments for documentation
COMMENT ON TABLE gym_manager_invitations IS 'Single-use invite codes for gym manager assignments with 60-second expiry';
COMMENT ON COLUMN gym_manager_invitations.invite_code IS '8-character unique invite code, auto-generated';
COMMENT ON COLUMN gym_manager_invitations.expires_at IS 'Invitation expires 60 seconds after creation';
COMMENT ON COLUMN gym_manager_invitations.gym_id IS 'Required: specific gym for manager assignment';
