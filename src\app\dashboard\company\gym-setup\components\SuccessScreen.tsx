'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Star, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface SuccessScreenProps {
  onContinue?: () => void;
}

export function SuccessScreen({ onContinue }: SuccessScreenProps) {
  const router = useRouter();

  const handleContinue = () => {
    if (onContinue) {
      onContinue();
    } else {
      // Dashboard'a yönlendir
      router.push('/dashboard/company');
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mx-auto max-w-4xl">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="max-w-md text-center">
            <div className="relative mb-8">
              <div className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-12 w-12 text-green-600" />
              </div>
              <div className="bg-primary absolute -right-2 -top-2 flex h-8 w-8 items-center justify-center rounded-full">
                <Star className="text-primary-foreground h-4 w-4" />
              </div>
            </div>
            <h2 className="text-primary mb-3 text-3xl font-bold">
              Salon Başarıyla Oluşturuldu!
            </h2>
            <p className="text-muted-foreground mb-8 leading-relaxed">
              Salonunuz başarıyla oluşturuldu ve sisteme kaydedildi. Artık salon
              yönetim paneline erişebilirsiniz.
            </p>
            <Button size="lg" className="shadow-lg" onClick={handleContinue}>
              <Zap className="mr-2 h-5 w-5" />
              Dashboard&apos;a Git
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
