/**
 * Inventory Management Actions
 * 
 * Server actions for inventory CRUD operations, stock tracking, and analytics
 */

'use server';

import { createAction } from '../../core/core';
import { ApiResponse } from '@/types/global/api';
import { 
  GymInventory, 
  InventoryTransaction, 
  InventoryCategory,
  InventoryFormData,
  InventoryTransactionFormData,
  InventoryAnalytics,
  InventoryFilters,
  InventoryListResponse
} from '@/types/database/equipment-inventory';
import { z } from 'zod';

// =============================================
// VALIDATION SCHEMAS
// =============================================

const inventorySchema = z.object({
  name: z.string().min(1, 'Ürün adı gereklidir').max(200, '<PERSON>rün adı en fazla 200 karakter olabilir'),
  category_id: z.string().uuid().optional(),
  description: z.string().optional(),
  sku: z.string().max(100).optional(),
  barcode: z.string().max(100).optional(),
  unit_type: z.enum(['piece', 'kg', 'liter', 'box', 'bottle']),
  current_stock: z.number().min(0, 'Stok miktarı negatif olamaz'),
  minimum_stock: z.number().min(0, 'Minimum stok negatif olamaz'),
  maximum_stock: z.number().min(0).optional(),
  unit_cost: z.number().min(0).optional(),
  selling_price: z.number().min(0).optional(),
  supplier_name: z.string().max(200).optional(),
  supplier_contact: z.string().optional(),
  expiry_date: z.string().optional(),
  location: z.string().max(200).optional(),
  image_url: z.string().url().optional(),
});

const transactionSchema = z.object({
  inventory_id: z.string().uuid('Geçersiz ürün ID'),
  transaction_type: z.enum(['purchase', 'sale', 'adjustment', 'waste', 'transfer']),
  quantity: z.number().int('Miktar tam sayı olmalıdır'),
  unit_cost: z.number().min(0).optional(),
  reference_number: z.string().max(100).optional(),
  notes: z.string().optional(),
  transaction_date: z.string().min(1, 'İşlem tarihi gereklidir'),
});

// =============================================
// INVENTORY CRUD OPERATIONS
// =============================================

/**
 * Get all inventory items for a gym with filters and analytics
 */
export async function getGymInventory(
  gymId: string,
  filters?: InventoryFilters
): Promise<ApiResponse<InventoryListResponse>> {
  return await createAction<InventoryListResponse>(async (_, supabase) => {
    let query = supabase
      .from('gym_inventory')
      .select(`
        *,
        category:inventory_categories(id, name, icon),
        transactions:inventory_transactions(
          id, transaction_type, quantity, transaction_date, total_cost
        )
      `)
      .eq('gym_id', gymId);

    // Apply filters
    if (filters?.category_id) {
      query = query.eq('category_id', filters.category_id);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }
    if (filters?.unit_type) {
      query = query.eq('unit_type', filters.unit_type);
    }
    if (filters?.low_stock) {
      query = query.filter('current_stock', 'lte', 'minimum_stock');
    }
    if (filters?.expired) {
      query = query.lt('expiry_date', new Date().toISOString().split('T')[0]);
    }
    if (filters?.search) {
      query = query.or(`name.ilike.%${filters.search}%,sku.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
    }

    const { data: inventory, error } = await query.order('created_at', { ascending: false });
    if (error) {
      throw new Error(`Depo listesi alınamadı: ${error.message}`);
    }

    // Add computed fields
    const inventoryWithComputed = inventory?.map(item => ({
      ...item,
      is_low_stock: item.current_stock <= item.minimum_stock,
      is_expired: item.expiry_date ? new Date(item.expiry_date) < new Date() : false,
    })) || [];

    // Get analytics
    const analytics = await getInventoryAnalytics(gymId, supabase);

    return {
      inventory: inventoryWithComputed,
      total: inventoryWithComputed.length,
      analytics,
    };
  });
}

/**
 * Get inventory analytics for dashboard
 */
async function getInventoryAnalytics(gymId: string, supabase: any): Promise<InventoryAnalytics> {
  // Get inventory stats
  const { data: inventoryStats } = await supabase
    .from('gym_inventory')
    .select('id, name, current_stock, minimum_stock, unit_cost, selling_price, expiry_date, status')
    .eq('gym_id', gymId);

  // Get transaction stats for current month
  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();
  const { data: monthlyTransactions } = await supabase
    .from('inventory_transactions')
    .select(`
      transaction_type,
      quantity,
      total_cost,
      inventory:gym_inventory!inner(gym_id)
    `)
    .eq('inventory.gym_id', gymId)
    .gte('transaction_date', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`);

  // Get top selling items (last 30 days)
  const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  const { data: salesData } = await supabase
    .from('inventory_transactions')
    .select(`
      inventory_id,
      quantity,
      inventory:gym_inventory!inner(id, name, gym_id)
    `)
    .eq('inventory.gym_id', gymId)
    .eq('transaction_type', 'sale')
    .gte('transaction_date', thirtyDaysAgo);

  const stats = inventoryStats || [];
  const totalItems = stats.length;
  const lowStockItems = stats.filter((item: any) => item.current_stock <= item.minimum_stock).length;
  const outOfStockItems = stats.filter((item: any) => item.current_stock === 0).length;
  const expiredItems = stats.filter((item: any) =>
    item.expiry_date && new Date(item.expiry_date) < new Date()
  ).length;

  const totalInventoryValue = stats.reduce((sum: any, item: any) =>
    sum + (item.current_stock * (item.unit_cost || 0)), 0
  );

  // Calculate monthly transaction stats
  const transactions = monthlyTransactions || [];
  const purchases = transactions.filter((t: any) => t.transaction_type === 'purchase').length;
  const sales = transactions.filter((t: any) => t.transaction_type === 'sale').length;
  const adjustments = transactions.filter((t: any) => t.transaction_type === 'adjustment').length;

  // Calculate top selling items
  const salesMap = new Map();
  salesData?.forEach((sale: any) => {
    const key = sale.inventory_id;
    if (salesMap.has(key)) {
      salesMap.set(key, {
        ...salesMap.get(key),
        sales_count: salesMap.get(key).sales_count + Math.abs(sale.quantity)
      });
    } else {
      salesMap.set(key, {
        ...sale.inventory,
        sales_count: Math.abs(sale.quantity)
      });
    }
  });

  const topSellingItems = Array.from(salesMap.values())
    .sort((a, b) => b.sales_count - a.sales_count)
    .slice(0, 5);

  // Get alerts
  const lowStockAlerts = stats.filter((item: any) =>
    item.current_stock <= item.minimum_stock && item.status === 'active'
  );

  const expiryAlerts = stats.filter((item: any) => {
    if (!item.expiry_date) return false;
    const expiryDate = new Date(item.expiry_date);
    const thirtyDaysFromNow = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    return expiryDate <= thirtyDaysFromNow && item.status === 'active';
  });

  return {
    total_items: totalItems,
    low_stock_items: lowStockItems,
    out_of_stock_items: outOfStockItems,
    expired_items: expiredItems,
    total_inventory_value: totalInventoryValue,
    monthly_transactions: {
      purchases,
      sales,
      adjustments,
    },
    top_selling_items: topSellingItems,
    low_stock_alerts: lowStockAlerts,
    expiry_alerts: expiryAlerts,
  };
}

/**
 * Get single inventory item by ID
 */
export async function getInventoryById(inventoryId: string): Promise<ApiResponse<GymInventory>> {
  return await createAction<GymInventory>(async (_, supabase) => {
    const { data, error } = await supabase
      .from('gym_inventory')
      .select(`
        *,
        category:inventory_categories(id, name, icon),
        transactions:inventory_transactions(*)
      `)
      .eq('id', inventoryId)
      .single();

    if (error) {
      throw new Error(`Ürün bulunamadı: ${error.message}`);
    }

    return {
      ...data,
      is_low_stock: data.current_stock <= data.minimum_stock,
      is_expired: data.expiry_date ? new Date(data.expiry_date) < new Date() : false,
    };
  });
}

/**
 * Create new inventory item
 */
export async function createInventory(
  gymId: string,
  formData: InventoryFormData
): Promise<ApiResponse<GymInventory>> {
  return await createAction<GymInventory>(async (_, supabase) => {
    // Validate input
    const validatedData = inventorySchema.parse(formData);

    const { data, error } = await supabase
      .from('gym_inventory')
      .insert({
        gym_id: gymId,
        ...validatedData,
        status: validatedData.current_stock > 0 ? 'active' : 'out_of_stock',
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Ürün oluşturulamadı: ${error.message}`);
    }

    return data;
  }, {
    requireAuth: true,
    revalidatePaths: [`/dashboard/gym/${gymId}/inventory`],
  });
}

/**
 * Update inventory item
 */
export async function updateInventory(
  inventoryId: string,
  formData: InventoryFormData,
  gymId?: string
): Promise<ApiResponse<GymInventory>> {
  return await createAction<GymInventory>(async (_, supabase) => {
    // Validate input
    const validatedData = inventorySchema.parse(formData);

    const { data, error } = await supabase
      .from('gym_inventory')
      .update({
        ...validatedData,
        status: validatedData.current_stock > 0 ? 'active' : 'out_of_stock',
      })
      .eq('id', inventoryId)
      .select()
      .single();

    if (error) {
      throw new Error(`Ürün güncellenemedi: ${error.message}`);
    }

    return data;
  }, {
    requireAuth: true,
    revalidatePaths: gymId ? [`/dashboard/gym/${gymId}/inventory`] : [],
  });
}

/**
 * Delete inventory item
 */
export async function deleteInventory(inventoryId: string, gymId?: string): Promise<ApiResponse<void>> {
  return await createAction<void>(async (_, supabase) => {
    const { error } = await supabase
      .from('gym_inventory')
      .delete()
      .eq('id', inventoryId);

    if (error) {
      throw new Error(`Ürün silinemedi: ${error.message}`);
    }
  }, {
    requireAuth: true,
    revalidatePaths: gymId ? [`/dashboard/gym/${gymId}/inventory`] : [],
  });
}

// =============================================
// INVENTORY CATEGORIES
// =============================================

/**
 * Get all inventory categories
 */
export async function getInventoryCategories(): Promise<ApiResponse<InventoryCategory[]>> {
  return await createAction<InventoryCategory[]>(async (_, supabase) => {
    const { data, error } = await supabase
      .from('inventory_categories')
      .select('*')
      .order('name');

    if (error) {
      throw new Error(`Kategoriler alınamadı: ${error.message}`);
    }

    return data || [];
  });
}

// =============================================
// INVENTORY TRANSACTIONS
// =============================================

/**
 * Get inventory transactions for a gym
 */
export async function getInventoryTransactions(
  gymId: string,
  filters?: { inventory_id?: string; transaction_type?: string; date_from?: string; date_to?: string }
): Promise<ApiResponse<InventoryTransaction[]>> {
  return await createAction<InventoryTransaction[]>(async (_, supabase) => {
    let query = supabase
      .from('inventory_transactions')
      .select(`
        *,
        inventory:gym_inventory!inner(id, name, gym_id)
      `)
      .eq('inventory.gym_id', gymId);

    // Apply filters
    if (filters?.inventory_id) {
      query = query.eq('inventory_id', filters.inventory_id);
    }
    if (filters?.transaction_type) {
      query = query.eq('transaction_type', filters.transaction_type);
    }
    if (filters?.date_from) {
      query = query.gte('transaction_date', filters.date_from);
    }
    if (filters?.date_to) {
      query = query.lte('transaction_date', filters.date_to);
    }

    const { data, error } = await query.order('transaction_date', { ascending: false });

    if (error) {
      throw new Error(`İşlem kayıtları alınamadı: ${error.message}`);
    }

    return data || [];
  });
}

/**
 * Create inventory transaction
 */
export async function createInventoryTransaction(
  formData: InventoryTransactionFormData,
  _performedBy?: string // Bu parametre artık kullanılmıyor, userId otomatik alınıyor
): Promise<ApiResponse<InventoryTransaction>> {
  return await createAction<InventoryTransaction>(async (_, supabase, userId) => {
    // Validate input
    const validatedData = transactionSchema.parse(formData);

    // Calculate total cost
    const totalCost = validatedData.unit_cost
      ? validatedData.unit_cost * Math.abs(validatedData.quantity)
      : undefined;

    const { data, error } = await supabase
      .from('inventory_transactions')
      .insert({
        ...validatedData,
        total_cost: totalCost,
        performed_by: userId, // createAction'dan gelen userId kullanılıyor
      })
      .select()
      .single();

    if (error) {
      throw new Error(`İşlem kaydı oluşturulamadı: ${error.message}`);
    }

    return data;
  }, { requireAuth: true }); // Auth gerekli olduğunu belirtiyoruz
}

/**
 * Quick stock adjustment
 */
export async function adjustInventoryStock(
  inventoryId: string,
  newStock: number,
  reason: string,
  _performedBy?: string // Bu parametre artık kullanılmıyor, userId otomatik alınıyor
): Promise<ApiResponse<InventoryTransaction>> {
  return await createAction<InventoryTransaction>(async (_, supabase, userId) => {
    // Get current stock
    const { data: currentItem, error: fetchError } = await supabase
      .from('gym_inventory')
      .select('current_stock')
      .eq('id', inventoryId)
      .single();

    if (fetchError) {
      throw new Error(`Mevcut stok bilgisi alınamadı: ${fetchError.message}`);
    }

    const difference = newStock - currentItem.current_stock;

    // Create adjustment transaction
    const { data, error } = await supabase
      .from('inventory_transactions')
      .insert({
        inventory_id: inventoryId,
        transaction_type: 'adjustment',
        quantity: difference,
        notes: reason,
        performed_by: userId, // createAction'dan gelen userId kullanılıyor
        transaction_date: new Date().toISOString().split('T')[0],
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Stok düzeltmesi yapılamadı: ${error.message}`);
    }

    return data;
  }, { requireAuth: true }); // Auth gerekli olduğunu belirtiyoruz
}

/**
 * Bulk stock update (for purchases)
 */
export async function bulkStockUpdate(
  updates: Array<{
    inventory_id: string;
    quantity: number;
    unit_cost?: number;
    reference_number?: string;
  }>,
  _performedBy?: string // Bu parametre artık kullanılmıyor, userId otomatik alınıyor
): Promise<ApiResponse<InventoryTransaction[]>> {
  return await createAction<InventoryTransaction[]>(async (_, supabase, userId) => {
    const transactions = updates.map(update => ({
      inventory_id: update.inventory_id,
      transaction_type: 'purchase' as const,
      quantity: update.quantity,
      unit_cost: update.unit_cost,
      total_cost: update.unit_cost ? update.unit_cost * update.quantity : undefined,
      reference_number: update.reference_number,
      performed_by: userId, // createAction'dan gelen userId kullanılıyor
      transaction_date: new Date().toISOString().split('T')[0],
    }));

    const { data, error } = await supabase
      .from('inventory_transactions')
      .insert(transactions)
      .select();

    if (error) {
      throw new Error(`Toplu stok güncellemesi yapılamadı: ${error.message}`);
    }

    return data || [];
  }, { requireAuth: true }); // Auth gerekli olduğunu belirtiyoruz
}
