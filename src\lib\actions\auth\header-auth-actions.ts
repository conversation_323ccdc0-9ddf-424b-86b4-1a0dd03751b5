'use server';

import { cache } from 'react';
import { getAuthenticatedUser, getUserRoles } from '@/lib/auth/server-auth';
import { getProfile } from '@/lib/actions/user/profile-actions';
import { getManagerGyms } from '@/lib/actions/dashboard/company/dashboard-actions';
import { getTrainerGyms } from '@/lib/actions/user/trainer-actions';
import { HeaderAuthStatus } from '@/components/header/shared/header-types';

/**
 * Cached header auth status - Next.js v15 optimized
 * Uses React cache() for request-level memoization
 */
export const getCachedHeaderAuthStatus = cache(async (): Promise<HeaderAuthStatus> => {
  try {
    // Auth kontrolü yap
    const user = await getAuthenticatedUser();

    if (!user) {
      return {
        authenticated: false,
        profile: null,
        roleOptions: [],
        companyGyms: [],
        trainerGyms: [],
        isManager: false,
        isTrainer: false,
        isMember: false,
      };
    }

    // <PERSON><PERSON>ıcı varsa profil ve rol bilgilerini çek
    const [profileResult, roles] = await Promise.all([
      getProfile(),
      getUserRoles(),
    ]);

    // Rol seçeneklerini oluştur
    const roleOptions = [];
    if (roles.includes('member')) {
      roleOptions.push({
        role: 'member',
        label: 'Üye',
        href: '/dashboard/member',
        available: true,
      });
    }
    if (roles.includes('trainer')) {
      roleOptions.push({
        role: 'trainer',
        label: 'Antrenör',
        href: '/dashboard/trainer',
        available: true,
      });
    }
    if (roles.includes('company_manager')) {
      roleOptions.push({
        role: 'company_manager',
        label: 'Şirket Paneli',
        href: '/dashboard/company',
        available: true,
      });
    }

    // Manager ise gym bilgilerini çek
    let companyGyms: any[] = [];
    if (roles.includes('company_manager')) {
      const gymsResult = await getManagerGyms();
      if (gymsResult.success && gymsResult.data) {
        companyGyms = gymsResult.data.map(gym => ({
          id: gym.id,
          name: gym.name,
          slug: gym.slug || '',
          city: gym.city || 'Bilinmeyen',
          district: gym.district || 'Bilinmeyen',
        }));
      }
    }

    // Trainer ise çalıştığı salonları çek
    let trainerGyms: any[] = [];
    if (roles.includes('trainer')) {
      const gymsResult = await getTrainerGyms();
      if (gymsResult.success && gymsResult.data) {
        trainerGyms = gymsResult.data
          .filter(gym => gym.status === 'active') // Sadece aktif salonlar
          .map(gym => ({
            id: gym.gym_id,
            name: gym.gym_name,
            slug: '', 
            city: gym.gym_city || 'Bilinmeyen',
            district: gym.gym_district || 'Bilinmeyen',
          }));
      }
    }

    return {
      authenticated: true,
      profile:
        profileResult.success && profileResult.data ? profileResult.data : null,
      roleOptions,
      companyGyms,
      trainerGyms,
      isManager: roles.includes('company_manager'),
      isTrainer: roles.includes('trainer'),
      isMember: roles.includes('member'),
    };
  } catch (error) {
    console.error('Header auth status check failed:', error);
    return {
      authenticated: false,
      profile: null,
      roleOptions: [],
      companyGyms: [],
      trainerGyms: [],
      isManager: false,
      isTrainer: false,
      isMember: false,
    };
  }
});


