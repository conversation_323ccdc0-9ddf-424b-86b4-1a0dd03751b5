import { RoleOption } from './header-types';
import { DASHBOARD_CONFIG } from './header-constants';

/**
 * Header Utilities - <PERSON><PERSON><PERSON>
 */

// Dashboard için role extraction
export const getCurrentRole = (
  pathname: string,
  roleOptions: RoleOption[]
): string => {
  // Safety checks
  if (!pathname || typeof pathname !== 'string') {
    return roleOptions[0]?.role || '';
  }

  if (pathname.includes('/dashboard/member'))
    return DASHBOARD_CONFIG.roles.member;
  // Trainer rotaları öncelikli kontrol: /dashboard/trainer, /dashboard/trainer/gym/*
  if (pathname.includes('/dashboard/trainer'))
    return DASHBOARD_CONFIG.roles.trainer;
  // Manager ana sayfası
  if (pathname.includes('/dashboard/company'))
    return DASHBOARD_CONFIG.roles.manager;
  // Manager için gym sayfaları: /dashboard/gym/*
  if (/^\/dashboard\/gym\//.test(pathname))
    return DASHBOARD_CONFIG.roles.manager;
  return roleOptions[0]?.role || '';
};

// Dashboard için gym ID extraction
export const getCurrentGymId = (
  pathname: string,
  currentRole: string
): string | null => {
  // Safety checks
  if (!pathname || typeof pathname !== 'string') {
    return null;
  }

  if (
    (currentRole === DASHBOARD_CONFIG.roles.manager ||
      currentRole === DASHBOARD_CONFIG.roles.trainer) &&
    pathname.includes('/gym/')
  ) {
    try {
      const gymIdMatch = pathname.match(DASHBOARD_CONFIG.gymPathPattern);
      if (gymIdMatch && gymIdMatch[1]) {
        // Additional validation - ensure the gym ID is a valid string
        const gymId = gymIdMatch[1].trim();
        return gymId && gymId.length > 0 ? gymId : null;
      }
    } catch (error) {
      console.error('Error extracting gym ID from pathname:', error, 'pathname:', pathname);
      return null;
    }
  }
  return null;
};

// User initials için utility
export const getUserInitials = (fullName: string, email: string): string => {
  if (fullName) {
    const names = fullName.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    }
    return fullName.slice(0, 2).toUpperCase();
  }
  return email.slice(0, 2).toUpperCase();
};
