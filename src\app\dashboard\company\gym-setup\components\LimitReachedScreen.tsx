'use client';

import { Al<PERSON><PERSON>riangle, Star, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useRouter } from 'next/navigation';

type SubscriptionTier = 'starter' | 'professional';

interface LimitReachedScreenProps {
  subscriptionTier: SubscriptionTier;
  maxGyms: number | null;
  currentGymCount: number;
}

export function LimitReachedScreen({
  subscriptionTier,
  maxGyms,
  currentGymCount,
}: LimitReachedScreenProps) {
  const router = useRouter();

  const getTierIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case 'starter':
        return <AlertTriangle className="h-5 w-5" />;
      case 'professional':
        return <Star className="h-5 w-5" />;
    }
  };

  const getTierName = (tier: SubscriptionTier) => {
    switch (tier) {
      case 'starter':
        return 'Başlangıç';
      case 'professional':
        return 'Profesyonel';
    }
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mx-auto max-w-4xl space-y-6">
        {/* Header */}
        <div className="mb-8 text-center">
          <div className="relative mb-8">
            <div className="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-orange-100">
              <AlertTriangle className="h-12 w-12 text-orange-600" />
            </div>
            <div className="bg-destructive absolute -right-2 -top-2 flex h-8 w-8 items-center justify-center rounded-full">
              <AlertTriangle className="text-destructive-foreground h-4 w-4" />
            </div>
          </div>
          <h1 className="text-primary mb-3 text-4xl font-bold">
            Salon Oluşturma Limiti
          </h1>
          <p className="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed">
            Mevcut paketinizle salon oluşturma limitinize ulaştınız. Daha fazla
            salon oluşturmak için paketinizi yükseltin.
          </p>
        </div>

        {/* Subscription Status Card */}
        <div className="mx-auto mb-8 max-w-2xl">
          <div className="bg-muted/30 border-muted rounded-xl border p-6">
            <div className="mb-4 flex items-center gap-3">
              {getTierIcon(subscriptionTier)}
              <div>
                <h2 className="text-primary text-xl font-semibold">
                  Mevcut Paketiniz: {getTierName(subscriptionTier)}
                </h2>
                <p className="text-muted-foreground">
                  Salon oluşturma durumunuz
                </p>
              </div>
            </div>

            {/* Current Status */}
            <div className="bg-destructive/10 border-destructive/20 mb-6 rounded-lg border p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-destructive/20 flex h-10 w-10 items-center justify-center rounded-full">
                    <AlertTriangle className="text-destructive h-5 w-5" />
                  </div>
                  <div>
                    <p className="text-destructive font-semibold">
                      Limit Doldu
                    </p>
                    <p className="text-muted-foreground text-sm">
                      {currentGymCount} / {maxGyms || '∞'} salon oluşturuldu
                    </p>
                  </div>
                </div>
                <Badge variant="destructive">
                  {getTierName(subscriptionTier)}
                </Badge>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row">
              <Button
                onClick={() => router.push('/onboarding')}
                className="flex-1 shadow-lg"
                size="lg"
              >
                <CreditCard className="mr-2 h-5 w-5" />
                Paket Yükselt
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/company')}
                className="flex-1"
                size="lg"
              >
                {"Dashboard'a Dön"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
