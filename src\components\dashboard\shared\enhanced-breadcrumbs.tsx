'use client';

import Link from 'next/link';
import { usePara<PERSON>, usePathname } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { getCityNameById } from '@/lib/constants/cityConstants';
import {
  getCurrentRole,
  getCurrentGymId,
} from '@/components/header/shared/header-utils';
import { RoleOption } from '@/components/header/shared/header-types';
import {
  ChevronsLeftRight,
  Crown,
  Dumbbell,
  User,
  Building2,
  Plus,
} from 'lucide-react';
import { getGymById } from '@/lib/actions/dashboard/company/gym-actions';
import { GymData } from '@/components/header/shared/header-types';

// Role icon mapping
const roleIconMap = {
  member: User,
  trainer: Dumbbell,
  company_manager: Crown, // ensure proper icon for company_manager
};

const getRoleIcon = (role: string) => {
  return roleIconMap[role as keyof typeof roleIconMap] || User;
};

interface BreadcrumbItemType {
  label: string;
  href?: string;
  isActive?: boolean;
  isGym?: boolean;
  gymName?: string;
  gymAddress?: string;
  gymSlug?: string;
}

interface EnhancedBreadcrumbsProps {
  roleOptions: RoleOption[];
  companyGyms: GymData[];
  trainerGyms: GymData[];
  isManager: boolean;
  isTrainer: boolean;
  isMember: boolean;
}

export function EnhancedBreadcrumbs({
  roleOptions,
  companyGyms,
  trainerGyms,
  isManager,
  isTrainer,
  isMember,
}: EnhancedBreadcrumbsProps) {
  const pathname = usePathname();

  // Calculate role and gym info from pathname
  const currentRole = getCurrentRole(pathname, roleOptions);
  const currentGymId = getCurrentGymId(pathname, currentRole);
  const params = useParams();
  const [gymName, setGymName] = useState<string>('');
  const [gymAddress, setGymAddress] = useState<string>('');
  const [gymSlug, setGymSlug] = useState<string>('');
  const [isLoadingGym, setIsLoadingGym] = useState(false);

  const gymId = params?.gymId as string;

  // Gym adını ve adres bilgilerini getir
  useEffect(() => {
    async function fetchGymInfo() {
      if (!gymId) return;

      setIsLoadingGym(true);
      try {
        const response = await getGymById(gymId);

        if (response.success && response.data) {
          const data = response.data;
          setGymName(data.name);
          setGymSlug(data.slug || '');

          // Adres bilgilerini formatla
          const addressParts: string[] = [];

          // Şehir bilgisini işle - eğer ID ise isim olarak dönüştür
          if (data.city) {
            // Önce sayı olup olmadığını kontrol et (ID olabilir)
            const cityName: string = getCityNameById(data.city) || data.city;
            addressParts.push(cityName);
          }

          if (data.district) addressParts.push(data.district);

          setGymAddress(addressParts.join(', '));
        }
      } catch (error) {
        console.error('Gym bilgileri alınırken hata:', error);
      } finally {
        setIsLoadingGym(false);
      }
    }

    fetchGymInfo();
  }, [gymId]);

  const breadcrumbs = useMemo(() => {
    const items: BreadcrumbItemType[] = [];

    // Dashboard dışındaki sayfalarda breadcrumb gösterme
    if (!pathname.startsWith('/dashboard')) {
      return items;
    }

    items.push({
      label: 'Panel',
      href: '/dashboard',
    });

    const pathSegments = pathname.split('/').filter(Boolean);

    // dashboard/member, dashboard/trainer veya dashboard/company
    if (pathSegments[1] === 'member') {
      items.push({
        label: 'Üye',
        href: '/dashboard/member',
      });
    } else if (pathSegments[1] === 'trainer') {
      items.push({
        label: 'Antrenör',
        href: '/dashboard/trainer',
      });
    } else if (pathSegments[1] === 'company') {
      items.push({
        label: 'Yönetici',
        href: '/dashboard/company',
      });

      // company/gym/[gymId] yapısı
      if (pathSegments[2] === 'gym' && gymId) {
        // Gym adı ve adres bilgisi (loading durumunda placeholder)
        const gymLabel = isLoadingGym
          ? 'Yükleniyor...'
          : gymName
            ? gymAddress
              ? `${gymName} (${gymAddress})`
              : gymName
            : 'Salon';

        items.push({
          label: gymLabel,
          href: `/dashboard/gym/${gymId}`,
          isGym: true,
          gymName: gymName || undefined,
          gymAddress: gymAddress || undefined,
          gymSlug: gymSlug || undefined,
        });

        // Alt sayfalar
        if (pathSegments[4]) {
          const subPage = pathSegments[4];
          const subPageLabels: Record<string, string> = {
            analytics: 'Analitik',
            members: 'Üyeler',
            trainers: 'Antrenörler',
            packages: 'Paketler',
            attendance: 'Devam Takibi',
            finance: 'Finansal Özet',
            settings: 'Ayarlar',
            subscriptions: 'Abonelikler',
            staff: 'Personel',
            invitations: 'Davetler',
            appointments: 'Randevular',
          };

          items.push({
            label: subPageLabels[subPage] || subPage,
            href: `/dashboard/gym/${gymId}/${subPage}`,
            isActive: !pathSegments[5], // 5. segment yoksa aktif
          });

          // Alt alt sayfalar (örn: members/addmember)
          if (pathSegments[5]) {
            const subSubPage = pathSegments[5];
            const subSubPageLabels: Record<string, string> = {
              addmember: 'Üye Ekle',
              edit: 'Düzenle',
              view: 'Görüntüle',
              add: 'Ekle',
              create: 'Oluştur',
            };

            items.push({
              label: subSubPageLabels[subSubPage] || subSubPage,
              isActive: true,
            });
          }
        }
      }
    }

    // Son item'ı aktif olarak işaretle (eğer zaten işaretlenmemişse)
    if (items.length > 0 && !items[items.length - 1].isActive) {
      items[items.length - 1].isActive = true;
      delete items[items.length - 1].href;
    }

    return items;
  }, [pathname, gymId, gymName, gymAddress, gymSlug, isLoadingGym]);

  // Not returning early here; selectors are also rendered within this component.

  const currentRoleOption = roleOptions.find(
    option => option.role === currentRole
  );

  const renderRoleSelector = () => {
    // Rol seçiciyi her zaman göster - tek rol olsa bile kullanıcı hangi rolde olduğunu görebilir
    if (roleOptions.length === 0 || !(isMember || isTrainer || isManager)) {
      return null;
    }

    return (
      <div className="flex h-14 items-center">
        {/* Rol adı - Ana sayfaya giden link */}
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Link
              href={currentRoleOption?.href || '/dashboard'}
              className={`hover:text-primary hover:bg-accent/50 flex h-full items-center space-x-2 px-3 text-sm font-medium transition-colors ${
                roleOptions.length > 1 ? 'rounded-l-md' : 'rounded-md'
              }`}
            >
              {currentRoleOption && (
                <>
                  {(() => {
                    const IconComponent = getRoleIcon(currentRoleOption.role);
                    return <IconComponent className="h-4 w-4" />;
                  })()}
                  <span>{currentRoleOption.label}</span>
                </>
              )}
            </Link>
          </TooltipTrigger>
          <TooltipContent>
            <p>{currentRoleOption?.label} ana sayfasına git</p>
          </TooltipContent>
        </Tooltip>

        {/* Dropdown ikonu - Sadece birden fazla rol varsa göster */}
        {roleOptions.length > 1 && (
          <DropdownMenu>
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="hover:text-primary hover:bg-accent/50 border-border/50 h-full rounded-r-md border-l px-2 transition-colors"
                  >
                    <ChevronsLeftRight className="h-3 w-3 rotate-90" />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent>
                <p>Rol değiştir</p>
              </TooltipContent>
            </Tooltip>
            <DropdownMenuContent align="start" className="w-48">
              <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">
                Rol Değiştir
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {roleOptions.map(option => (
                <DropdownMenuItem key={option.role} asChild>
                  <Link
                    href={option.href}
                    className={`flex w-full cursor-pointer items-center justify-between px-2 py-2 ${
                      option.role === currentRole ? 'bg-accent' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-2">
                      {(() => {
                        const IconComponent = getRoleIcon(option.role);
                        return <IconComponent className="h-4 w-4" />;
                      })()}
                      <span>{option.label}</span>
                    </div>
                    {option.role === currentRole && (
                      <Badge variant="secondary" className="text-xs">
                        Aktif
                      </Badge>
                    )}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    );
  };

  const renderGymSelector = () => {
    // Manager için salon seçici
    if (
      isManager &&
      currentRole === 'company_manager' &&
      companyGyms.length > 0
    ) {
      const currentGym = companyGyms.find(gym => gym.id === currentGymId);

      return (
        <DropdownMenu>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hover:text-primary hover:bg-accent/50 flex h-14 items-center space-x-2 px-3 text-sm font-medium transition-colors"
                >
                  <Building2 className="h-4 w-4 flex-shrink-0" />
                  <div className="flex min-w-0 flex-col items-start">
                    <span className="truncate text-sm font-medium">
                      {currentGym?.name || 'Salon Seç'}
                    </span>
                    {currentGym && (
                      <span className="text-muted-foreground truncate text-xs">
                        {getCityNameById(currentGym.city) || currentGym.city},{' '}
                        {currentGym.district}
                      </span>
                    )}
                  </div>
                  <ChevronsLeftRight className="h-3 w-3 flex-shrink-0 rotate-90" />
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Salon değiştir</p>
            </TooltipContent>
          </Tooltip>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">
              Salon Seç
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {companyGyms.map(gym => (
              <DropdownMenuItem key={gym.id} asChild>
                <Link
                  href={`/dashboard/gym/${gym.id}`}
                  className={`flex w-full cursor-pointer items-center justify-between px-2 py-2 ${
                    gym.id === currentGymId ? 'bg-accent' : ''
                  }`}
                >
                  <div className="flex flex-col items-start space-y-0.5">
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4" />
                      <span className="truncate">{gym.name}</span>
                    </div>
                    <span className="text-muted-foreground text-xs lowercase">
                      {getCityNameById(gym.city) || gym.city}, {gym.district}
                    </span>
                  </div>
                  {gym.id === currentGymId && (
                    <Badge variant="secondary" className="text-xs">
                      Aktif
                    </Badge>
                  )}
                </Link>
              </DropdownMenuItem>
            ))}

            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link
                href="/dashboard/company/gym-setup"
                className="flex w-full cursor-pointer items-center space-x-2 px-2 py-2"
              >
                <Plus className="h-4 w-4" />
                <span>Yeni Salon Ekle</span>
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    // Antrenör için salon seçici - Artık /dashboard/gym/[gymId] kullanıyor
    if (isTrainer && currentRole === 'trainer' && trainerGyms.length > 0) {
      const currentGym = trainerGyms.find(gym => gym.id === currentGymId);

      return (
        <DropdownMenu>
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="hover:text-primary hover:bg-accent/50 flex h-14 items-center space-x-2 px-3 text-sm font-medium transition-colors"
                >
                  <Building2 className="h-4 w-4 flex-shrink-0" />
                  <div className="flex min-w-0 flex-col items-start">
                    <span className="truncate text-sm font-medium">
                      {currentGym?.name || gymName || 'Salon Seç'}
                    </span>
                    {currentGym && (
                      <span className="text-muted-foreground truncate text-xs">
                        {getCityNameById(currentGym.city) || currentGym.city},{' '}
                        {currentGym.district}
                      </span>
                    )}
                  </div>
                  <ChevronsLeftRight className="h-3 w-3 flex-shrink-0 rotate-90" />
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p>Salon değiştir</p>
            </TooltipContent>
          </Tooltip>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel className="text-muted-foreground text-xs font-medium">
              Salon Seç
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {trainerGyms.map(gym => (
              <DropdownMenuItem key={gym.id} asChild>
                <Link
                  href={`/dashboard/gym/${gym.id}`}
                  className={`flex w-full cursor-pointer items-center justify-between px-2 py-2 ${
                    gym.id === currentGymId ? 'bg-accent' : ''
                  }`}
                >
                  <div className="flex flex-col items-start space-y-0.5">
                    <div className="flex items-center space-x-2">
                      <Building2 className="h-4 w-4" />
                      <span className="truncate">{gym.name}</span>
                    </div>
                    <span className="text-muted-foreground text-xs lowercase">
                      {getCityNameById(gym.city) || gym.city}, {gym.district}
                    </span>
                  </div>
                  {gym.id === currentGymId && (
                    <Badge variant="secondary" className="text-xs">
                      Aktif
                    </Badge>
                  )}
                </Link>
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    return null;
  };

  const renderBreadcrumbContent = (item: BreadcrumbItemType) => {
    if (item.isGym && item.gymName) {
      return (
        <div className="from-primary/5 to-primary/10 hover:from-primary/10 hover:to-primary/15 flex items-center rounded-lg bg-gradient-to-r px-3 py-2 transition-all duration-200">
          <div className="bg-primary/20 flex h-6 w-6 items-center justify-center rounded-full">
            <Building2 className="h-3.5 w-3.5" />
          </div>
          <div className="flex min-w-0 flex-col">
            <span className="text-foreground truncate text-sm leading-tight font-semibold">
              {item.gymName}
            </span>
            {item.gymAddress && (
              <span className="text-muted-foreground truncate text-xs leading-tight">
                {item.gymAddress}
              </span>
            )}
          </div>
        </div>
      );
    }
    return item.label;
  };

  // Create enhanced breadcrumb items with role and gym selectors
  const enhancedBreadcrumbs = [];

  // Add role selector as first breadcrumb item
  const roleSelector = renderRoleSelector();
  if (roleSelector) {
    enhancedBreadcrumbs.push({
      type: 'role-selector',
      content: roleSelector,
    });
  }

  // Add gym selector as second breadcrumb item (if applicable)
  const gymSelector = renderGymSelector();
  if (gymSelector) {
    enhancedBreadcrumbs.push({
      type: 'gym-selector',
      content: gymSelector,
    });
  }

  // Add remaining breadcrumbs (excluding role and gym items that are now in selectors)
  const filteredBreadcrumbs = breadcrumbs.filter(
    (item: BreadcrumbItemType) =>
      item.label !== 'Panel' &&
      item.label !== 'Üye' &&
      item.label !== 'Antrenör' &&
      item.label !== 'Yönetici' &&
      !item.isGym
  );

  // If there are no selectors and no breadcrumbs, render nothing
  if (enhancedBreadcrumbs.length === 0 && filteredBreadcrumbs.length === 0) {
    return null;
  }

  return (
    <TooltipProvider>
      <Breadcrumb className="hidden md:block">
        <BreadcrumbList className="flex items-center gap-0">
          {enhancedBreadcrumbs.map((item, index) => (
            <div key={`selector-${index}`} className="flex items-center">
              <BreadcrumbItem className="flex items-center">
                {item.content}
              </BreadcrumbItem>
              {(index < enhancedBreadcrumbs.length - 1 ||
                filteredBreadcrumbs.length > 0) && (
                <BreadcrumbSeparator className="mx-2" />
              )}
            </div>
          ))}
          {filteredBreadcrumbs.map(
            (item: BreadcrumbItemType, index: number) => (
              <div key={`breadcrumb-${index}`} className="flex items-center">
                <BreadcrumbItem>
                  {item.isActive ? (
                    <Tooltip delayDuration={0}>
                      <TooltipTrigger asChild>
                        <BreadcrumbPage className="flex items-center">
                          {renderBreadcrumbContent(item)}
                        </BreadcrumbPage>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Mevcut sayfa: {item.label}</p>
                      </TooltipContent>
                    </Tooltip>
                  ) : (
                    <Tooltip delayDuration={0}>
                      <TooltipTrigger asChild>
                        <BreadcrumbLink asChild>
                          <Link href={item.href!} className="flex items-center">
                            {renderBreadcrumbContent(item)}
                          </Link>
                        </BreadcrumbLink>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{item.label} sayfasına git</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </BreadcrumbItem>
                {index < filteredBreadcrumbs.length - 1 && (
                  <BreadcrumbSeparator className="mx-2" />
                )}
              </div>
            )
          )}
        </BreadcrumbList>
      </Breadcrumb>
    </TooltipProvider>
  );
}
