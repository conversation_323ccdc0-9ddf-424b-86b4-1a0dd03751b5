'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trash } from 'lucide-react';
import {
  Mail,
  Building2,
  MapPin,
  Calendar,
  Check,
  X,
  Clock,
  AlertCircle,
  Send,
} from 'lucide-react';
import { toast } from 'sonner';

import { formatDateTime } from '@/lib/utils';
import { IncomingInvitation, OutgoingInvitation } from '@/lib/actions/gym_invitations/invitation-types';
import { acceptInvitation, cancelInvitation, rejectInvitation } from '@/lib/actions/gym_invitations/invitation-actions';


interface TrainerInvitationsClientProps {
  incomingInvitations: IncomingInvitation[];
  outgoingInvitations: OutgoingInvitation[];
}

export function TrainerInvitationsClient({
  incomingInvitations,
  outgoingInvitations,
}: TrainerInvitationsClientProps) {
  const [invitations, setInvitations] =
    useState<IncomingInvitation[]>(incomingInvitations);
  const [outgoingState, setOutgoingState] =
    useState<OutgoingInvitation[]>(outgoingInvitations);


  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge
            variant="outline"
            className="border-orange-600 text-orange-600"
          >
            Bekliyor
          </Badge>
        );
      case 'accepted':
        return (
          <Badge variant="default" className="bg-green-500">
            Kabul Edildi
          </Badge>
        );
      case 'rejected':
        return <Badge variant="destructive">Reddedildi</Badge>;
      case 'expired':
        return (
          <Badge variant="destructive" className="bg-gray-400">
            Süresi Doldu
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const isExpired = (expiresAt: string | null) => {
    if (!expiresAt) return false;
    return new Date(expiresAt) < new Date();
  };

  const handleAcceptInvitation = async (invitationId: string) => {
    try {
      const result = await acceptInvitation(invitationId);

      if (result.success) {
        toast.success(result.message || 'Davet kabul edildi');

        setInvitations(prev =>
          prev.map(inv =>
            inv.id === invitationId
              ? { ...inv, status: 'accepted' as const }
              : inv
          )
        );
      } else {
        toast.error(result.error || 'Davet kabul edilirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Accept invitation error:', error);
      toast.error('Davet kabul edilirken bir hata oluştu');
    }
  };

  const handleRejectInvitation = async (invitationId: string) => {
    try {
      const result = await rejectInvitation(invitationId);

      if (result.success) {
        toast.success(result.message || 'Davet reddedildi');

        setInvitations(prev =>
          prev.map(inv =>
            inv.id === invitationId
              ? { ...inv, status: 'rejected' as const }
              : inv
          )
        );
      } else {
        toast.error(result.error || 'Davet reddedilirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Reject invitation error:', error);
      toast.error('Davet reddedilirken bir hata oluştu');
    }
  };

  const handleDeleteInvitation = async (invitationId: string) => {
    try {
      const result = await cancelInvitation(invitationId);

      if (result.success) {
        toast.success(result.message || 'Davet silindi');

        setInvitations(prev => prev.filter(inv => inv.id !== invitationId));
        setOutgoingState(prev => prev.filter(inv => inv.id !== invitationId));
      } else {
        toast.error(result.error || 'Davet silinirken bir hata oluştu');
      }
    } catch (error) {
      console.error('Delete invitation error:', error);
      toast.error('Davet silinirken bir hata oluştu');
    }
  };

  if (invitations.length === 0 && outgoingState.length === 0) {
    return (
      <div className="py-12 text-center">
        <Mail className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
        <h3 className="text-foreground mb-2 text-lg font-medium">
          Henüz davet bulunmuyor
        </h3>
        <p className="text-muted-foreground">
          Salon yöneticilerinden gelen davetler burada görünecektir.
        </p>
      </div>
    );
  }

  const pendingInvitations = invitations.filter(inv => {
    const expired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status === 'pending' && !expired;
  });
  const respondedInvitations = invitations.filter(inv => {
    const expired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status !== 'pending' || expired;
  });

  // Outgoing lists
  const pendingOutgoingInvitations = outgoingState.filter(inv => {
    const expired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status === 'pending' && !expired;
  });
  const processedOutgoingInvitations = outgoingState.filter(inv => {
    const expired = inv.expires_at && new Date(inv.expires_at) < new Date();
    return inv.status !== 'pending' || expired;
  });

  return (
    <div className="space-y-6">
      {/* Pending Invitations */}
      {pendingInvitations.length > 0 && (
        <div>
          <h2 className="mb-4 flex items-center text-xl font-semibold">
            <Clock className="mr-2 h-5 w-5 text-orange-600 dark:text-orange-400" />
            Bekleyen Davetler ({pendingInvitations.length})
          </h2>
          <div className="grid gap-4">
            {pendingInvitations.map(invitation => {
              const expired = isExpired(invitation.expires_at);

              return (
                <Card
                  key={invitation.id}
                  className={`${expired ? 'opacity-60' : 'hover:shadow-md'} transition-shadow`}
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="flex items-center">
                          <Building2 className="mr-2 h-5 w-5" />
                          {invitation.gym.name}
                        </CardTitle>
                        <CardDescription className="mt-1 flex items-center">
                          <MapPin className="mr-1 h-4 w-4" />
                          {invitation.gym.address || 'Adres bilgisi bulunmuyor'}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        {expired && (
                          <Badge variant="destructive" className="text-xs">
                            <AlertCircle className="mr-1 h-3 w-3" />
                            Süresi Dolmuş
                          </Badge>
                        )}
                        {getStatusBadge(invitation.status)}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
                        <div>
                          <p className="text-foreground font-medium">
                            Davet Eden
                          </p>
                          <p className="text-muted-foreground">
                            {invitation.profile.full_name}
                          </p>
                        </div>
                        <div>
                          <p className="text-foreground font-medium">
                            Davet Tarihi
                          </p>
                          <div className="text-muted-foreground flex items-center">
                            <Calendar className="mr-1 h-3 w-3" />
                            {formatDateTime(invitation.created_at)}
                          </div>
                          <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                            Son tarih: {formatDateTime(invitation.expires_at)}
                          </p>
                        </div>
                      </div>

                      {invitation.message && (
                        <div className="bg-muted/50 rounded-lg p-3">
                          <p className="text-muted-foreground text-sm">
                            {invitation.message}
                          </p>
                        </div>
                      )}

                      {/* Outgoing Invitations */}
                      {outgoingState.length > 0 && (
                        <div>
                          <h2 className="mb-4 flex items-center text-xl font-semibold">
                            <Send className="mr-2 h-5 w-5" />
                            Gönderdiğiniz Davetler ({outgoingState.length})
                          </h2>

                          {/* Pending Outgoing */}
                          {pendingOutgoingInvitations.length > 0 && (
                            <div className="space-y-4">
                              <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium">Bekleyen</h3>
                                <Badge className="border-yellow-200 bg-yellow-100 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200">
                                  {pendingOutgoingInvitations.length} Davet
                                </Badge>
                              </div>
                              <div className="grid gap-4">
                                {pendingOutgoingInvitations.map(invitation => (
                                  <Card key={invitation.id}>
                                    <CardContent className="pt-6">
                                      <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                          <h4 className="flex items-center font-semibold">
                                            <Building2 className="mr-2 h-4 w-4" />
                                            {invitation.gym.name}
                                          </h4>
                                          <p className="text-muted-foreground mt-1 flex items-center text-sm">
                                            <MapPin className="mr-1 h-3 w-3" />
                                            {invitation.gym.address || 'Adres bilgisi bulunmuyor'}
                                          </p>
                                          <p className="text-muted-foreground mt-1 text-xs">
                                            Gönderim: {formatDateTime(invitation.created_at)}
                                          </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          {getStatusBadge(invitation.status)}
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => handleDeleteInvitation(invitation.id)}
                                            className="hover:bg-destructive/10"
                                          >
                                            <Trash className="text-destructive h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Processed Outgoing */}
                          {processedOutgoingInvitations.length > 0 && (
                            <div className="mt-6 space-y-4">
                              <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium">Yanıtlanan</h3>
                                <Badge variant="secondary">
                                  {processedOutgoingInvitations.length} Davet
                                </Badge>
                              </div>
                              <div className="grid gap-4">
                                {processedOutgoingInvitations.map(invitation => (
                                  <Card key={invitation.id} className="opacity-75">
                                    <CardContent className="pt-6">
                                      <div className="flex items-center justify-between">
                                        <div className="flex-1">
                                          <h4 className="flex items-center font-semibold">
                                            <Building2 className="mr-2 h-4 w-4" />
                                            {invitation.gym.name}
                                          </h4>
                                          <p className="text-muted-foreground mt-1 flex items-center text-sm">
                                            <MapPin className="mr-1 h-3 w-3" />
                                            {invitation.gym.address || 'Adres bilgisi bulunmuyor'}
                                          </p>
                                          <p className="text-muted-foreground mt-1 text-xs">
                                            Gönderim: {formatDateTime(invitation.created_at)}
                                          </p>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          {getStatusBadge(invitation.status)}
                                          <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => handleDeleteInvitation(invitation.id)}
                                            className="hover:bg-destructive/10"
                                          >
                                            <Trash className="text-destructive h-4 w-4" />
                                          </Button>
                                        </div>
                                      </div>
                                    </CardContent>
                                  </Card>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      {!expired && (
                        <div className="flex justify-end gap-3">
                          <Button
                            variant="outline"
                            onClick={() =>
                              handleRejectInvitation(invitation.id)
                            }
                            className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <X className="mr-2 h-4 w-4" />
                            Reddet
                          </Button>
                          <Button
                            onClick={() =>
                              handleAcceptInvitation(invitation.id)
                            }
                          >
                            <Check className="mr-2 h-4 w-4" />
                            Kabul Et
                          </Button>
                        </div>
                      )}

                      {expired && (
                        <div className="py-2 text-center">
                          <p className="text-sm text-red-600 dark:text-red-400">
                            Bu davet süresi dolmuştur. Salon yöneticisi ile
                            iletişime geçin.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Responded Invitations */}
      {respondedInvitations.length > 0 && (
        <div>
          <h2 className="mb-4 text-xl font-semibold">Yanıtlanan Davetler</h2>
          <div className="grid gap-4">
            {respondedInvitations.map(invitation => (
              <Card key={invitation.id} className="opacity-75">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h3 className="flex items-center font-semibold">
                        <Building2 className="mr-2 h-4 w-4" />
                        {invitation.gym.name}
                      </h3>
                      <p className="text-muted-foreground mt-1 flex items-center text-sm">
                        <MapPin className="mr-1 h-3 w-3" />
                        {invitation.gym.address || 'Adres bilgisi bulunmuyor'}
                      </p>
                      <p className="text-muted-foreground mt-1 text-xs">
                        Davet tarihi: {formatDateTime(invitation.created_at)}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(invitation.status)}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDeleteInvitation(invitation.id)}
                        className="hover:bg-destructive/10"
                      >
                        <Trash className="text-destructive h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
