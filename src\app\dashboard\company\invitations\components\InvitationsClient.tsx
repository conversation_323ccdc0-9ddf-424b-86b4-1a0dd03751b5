'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  UserPlus,
  Copy,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Building2,
  Users,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { Gyms } from '@/types/database/tables';
import { GymManagerInvitationWithDetails, cancelInvitation } from '@/lib/actions/dashboard/company/gym-manager-invitation-actions';
import { CreateInvitationDialog } from './CreateInvitationDialog';

interface InvitationsClientProps {
  gyms: Gyms[];
  invitations: GymManagerInvitationWithDetails[];
}

export function InvitationsClient({ gyms, invitations }: InvitationsClientProps) {
  const [cancellingId, setCancellingId] = useState<string | null>(null);

  const handleCancelInvitation = async (invitationId: string) => {
    setCancellingId(invitationId);
    try {
      const result = await cancelInvitation(invitationId);
      if (result.success) {
        toast.success('Davet kodu iptal edildi');
        // Sayfayı yenile
        window.location.reload();
      } else {
        toast.error(result.error || 'Davet kodu iptal edilemedi');
      }
    } catch (error) {
      console.error('Cancel invitation error:', error);
      toast.error('Davet kodu iptal edilirken bir hata oluştu');
    } finally {
      setCancellingId(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600 border-yellow-600"><Clock className="w-3 h-3 mr-1" />Bekliyor</Badge>;
      case 'accepted':
        return <Badge variant="outline" className="text-green-600 border-green-600"><CheckCircle className="w-3 h-3 mr-1" />Kabul Edildi</Badge>;
      case 'expired':
        return <Badge variant="outline" className="text-gray-600 border-gray-600"><AlertCircle className="w-3 h-3 mr-1" />Süresi Doldu</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="text-red-600 border-red-600"><XCircle className="w-3 h-3 mr-1" />İptal Edildi</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const copyInviteCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success('Davet kodu kopyalandı!');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Salon Yöneticisi Davetleri
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Salonlarınız için yönetici davet kodları oluşturun ve yönetin
          </p>
        </div>
        
        <CreateInvitationDialog
          gyms={gyms}
          onSuccess={() => window.location.reload()}
        />
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Toplam Salon</p>
                <p className="text-2xl font-bold">{gyms.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Bekleyen Davetler</p>
                <p className="text-2xl font-bold">
                  {invitations.filter(inv => inv.status === 'pending' && !isExpired(inv.expires_at)).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Kabul Edilen</p>
                <p className="text-2xl font-bold">
                  {invitations.filter(inv => inv.status === 'accepted').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Aktif Yöneticiler</p>
                <p className="text-2xl font-bold">
                  {gyms.filter(gym => gym.manager_profile_id).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Invitations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Davet Kodları</CardTitle>
        </CardHeader>
        <CardContent>
          {invitations.length === 0 ? (
            <div className="text-center py-8">
              <UserPlus className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                Henüz davet kodu yok
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Salonlarınız için yönetici davet kodu oluşturmaya başlayın.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Salon</TableHead>
                  <TableHead>Davet Kodu</TableHead>
                  <TableHead>Durum</TableHead>
                  <TableHead>Oluşturulma</TableHead>
                  <TableHead>Son Geçerlilik</TableHead>
                  <TableHead>Kullanıcı</TableHead>
                  <TableHead>İşlemler</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invitations.map((invitation) => (
                  <TableRow key={invitation.id}>
                    <TableCell className="font-medium">
                      {invitation.gym.name}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <code className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-sm font-mono">
                          {invitation.invite_code}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyInviteCode(invitation.invite_code)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(invitation.status)}
                    </TableCell>
                    <TableCell>
                      {formatDate(invitation.created_at)}
                    </TableCell>
                    <TableCell>
                      <span className={isExpired(invitation.expires_at) ? 'text-red-600' : ''}>
                        {formatDate(invitation.expires_at)}
                      </span>
                    </TableCell>
                    <TableCell>
                      {invitation.user ? (
                        <div>
                          <div className="font-medium">{invitation.user.full_name}</div>
                          <div className="text-sm text-gray-500">{invitation.user.email}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {invitation.status === 'pending' && !isExpired(invitation.expires_at) && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancelInvitation(invitation.id)}
                          disabled={cancellingId === invitation.id}
                        >
                          {cancellingId === invitation.id ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              İptal Ediliyor...
                            </>
                          ) : (
                            'İptal Et'
                          )}
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
