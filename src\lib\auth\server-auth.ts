'use server';
import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { AuthUser } from '@supabase/supabase-js';
import { createAction} from '@/lib/actions/core/core';

/**
 * Server component'lerde kullanılmak üzere optimize edilmiş auth utilities
 */

export interface AuthResult {
  user: AuthUser | null;
  roles: string[];
}

/**
 * Middleware tarafından set edilen user bilgisini al
 * Performance için middleware'den gelen bilgiyi kullan
 */
export async function getAuthenticatedUser(): Promise<AuthUser | null> {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  return user;
}

/**
 * Kullanıcının rollerini getir (createAction pattern ile optimize edilmiş)
 */
export async function getUserRoles(): Promise<string[]> {
  const result = await createAction(
    async (_, supabase, userId) => {
      const roles: string[] = [];

      // Para<PERSON>l olarak tüm rolleri kontrol et
      const [member<PERSON><PERSON><PERSON>, trainer<PERSON>heck, companyCheck, gymManagerCheck] = await Promise.all([
        supabase
          .from('member_details')
          .select('profile_id')
          .eq('profile_id', userId)
          .maybeSingle(),
        supabase
          .from('trainer_details')
          .select('profile_id')
          .eq('profile_id', userId)
          .maybeSingle(),
        supabase
          .from('companies')
          .select('manager_profile_id')
          .eq('manager_profile_id', userId)
          .maybeSingle(),
        supabase
          .from('gyms')
          .select('manager_profile_id')
          .eq('manager_profile_id', userId)
          .maybeSingle(),
      ]);

      if (memberCheck.data) roles.push('member');
      if (trainerCheck.data) roles.push('trainer');
      if (companyCheck.data) roles.push('company_manager');
      if (gymManagerCheck.data) roles.push('gym_manager');
      return roles;
    },
    { requireAuth: false }
  );

  return result.success ? result.data! : [];
}

/**
 * Role kontrolü ile birlikte auth kontrolü
 */
export async function requireRole(requiredRole: string): Promise<AuthResult> {
  const user = await getAuthenticatedUser();

  if (!user) {
    redirect('/auth/login');
  }

  const roles = await getUserRoles();

  if (!roles.includes(requiredRole)) {
    redirect(
      `/error?type=access_denied&message=${encodeURIComponent(`Bu sayfaya erişmek için ${requiredRole} rolü gerekiyor`)}`
    );
  }

  return { user, roles };
}

/**
 * Gym access kontrolü (trainer için)
 * Gym assignment data'sını da döndürür
 */
export async function requireTrainerGymAccess(gymId: string): Promise<
  AuthResult & {
    gymAssignment: { id: string };
  }
> {
  const { user, roles } = await requireRole('trainer');

  if (!user) {
    redirect('/auth/login');
  }

  const result = await createAction(async (_, supabase) => {
    const { data, error } = await supabase
      .from('gym_trainers')
      .select('id')
      .eq('trainer_profile_id', user.id)
      .eq('gym_id', gymId)
      .eq('status', 'active')
      .single();

    if (error || !data) {
      throw new Error('Bu salona erişim yetkiniz bulunmuyor');
    }

    return data;
  });

  if (!result.success) {
    redirect(
      `/error?type=gym_access_denied&message=${encodeURIComponent(result.error || 'Bu salona erişim yetkiniz bulunmuyor')}`
    );
  }

  return { user, roles, gymAssignment: result.data! };
}

// Antrenör yetki tipleri
export interface TrainerPermissions {
  members: {
    read: boolean;
    create: boolean;
    update: boolean;
  };
  packages: {
    create: boolean;
    update: boolean;
  };
  appointments: {
    read: boolean;
    create: boolean;
    delete: boolean;
    update: boolean;
  };
}

/**
 * Check manager active subscription
 */
export async function checkManagerActiveSubscription(): Promise<boolean> {
  const result = await createAction<boolean>(
    async (_, supabase, userId) => {
      const { data, error } = await supabase
        .from('companies')
        .select('subscription_status')
        .eq('manager_profile_id', userId)
        .eq('subscription_status', 'active')
        .maybeSingle();

      return !error && !!data;
    }
  );

  return result.success ? (result.data ?? false) : false;
}

/**
 * Check manager gym access (company manager or gym manager)
 */
export async function checkManagerGymAccess(gymId: string): Promise<boolean> {
  const result = await createAction<boolean>(
    async (_, supabase, userId) => {
      const { data, error } = await supabase
        .from('gyms')
        .select(`
          id,
          manager_profile_id,
          company:companies!inner(
            id,
            manager_profile_id
          )
        `)
        .eq('id', gymId)
        .single();

      if (error || !data) return false;

      // Company manager veya gym manager erişimi
      return (data.company as any).manager_profile_id === userId || data.manager_profile_id === userId;
    }
  );

  return result.success ? (result.data ?? false) : false;
}

/**
 * Check trainer gym access
 */
export async function checkTrainerGymAccess( gymId: string): Promise<boolean> {
  const result = await createAction<boolean>(
    async (_, supabase, userId) => {
      const { data, error } = await supabase
        .from('gym_trainers')
        .select('id')
        .eq('trainer_profile_id', userId)
        .eq('gym_id', gymId)
        .eq('status', 'active')
        .single();

      return !error && !!data;
    }
  );

  return result.success ? (result.data ?? false) : false;
}
