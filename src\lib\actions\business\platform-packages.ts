'use server';

import { ApiResponse } from '@/types/global/api';
import { PlatformPackages } from '@/types/database/tables';
import { getSupabaseAdmin } from '@/lib/supabase/admin';

export async function getAllPlatformPackages(): Promise<
  ApiResponse<PlatformPackages[]>
> {
  try {
    const supabase = await getSupabaseAdmin();
    const { data, error } = await supabase
      .from('platform_packages')
      .select('*')
      .eq('is_active', true)

    if (error) {
      return {
        success: false,
        error: `Platform paketleri getirilirken hata: ${error.message}`,
      };
    }

    // Normalize and stabilize ordering
    const TIER_ORDER = ['free', 'starter', 'professional'] as const;
    // Only support yearly and lifetime (null). Exclude monthly from results.
    const DURATION_ORDER = [null, 'yearly'] as const; // null for free tier (lifetime), yearly for paid tiers

    const filtered = (data || []).filter((row) => row.duration !== 'monthly');

    const normalized: PlatformPackages[] = filtered.map((row) => ({
      ...row,
      // Supabase numeric types may arrive as strings at runtime
      price: typeof (row as any).price === 'string' ? Number((row as any).price) : (row.price as number),
      // keep other fields as-is (tier/duration remain strings)
    }))
    // stable sort by tier and duration regardless of DB collation
    .sort((a, b) => {
      const tA = TIER_ORDER.indexOf(a.tier as (typeof TIER_ORDER)[number]);
      const tB = TIER_ORDER.indexOf(b.tier as (typeof TIER_ORDER)[number]);
      if (tA !== tB) return tA - tB;
      
      // Handle null duration (free tier) properly
      const dAIdx = DURATION_ORDER.findIndex(d => d === a.duration);
      const dBIdx = DURATION_ORDER.findIndex(d => d === b.duration);
      const safeA = dAIdx === -1 ? 99 : dAIdx; // unknowns go last
      const safeB = dBIdx === -1 ? 99 : dBIdx;
      return safeA - safeB;
    });

    return {
      success: true,
      data: normalized,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bilinmeyen hata oluştu',
    };
  }
}

/**
 * Platform paket ID'sine göre paket detaylarını getirir
 * @param packageId - Platform paket ID'si
 * @returns Platform paket detayları
 */
export async function getPackageDataById(
  packageId: string
): Promise<ApiResponse<PlatformPackages>> {
  try {
    if (!packageId) {
      return {
        success: false,
        error: 'Paket ID gereklidir.',
      };
    }

    const supabase = await getSupabaseAdmin();
    const { data, error } = await supabase
      .from('platform_packages')
      .select('*')
      .eq('id', packageId)
      .eq('is_active', true)
      .single();

    if (error) {
      return {
        success: false,
        error: `Platform paketi alınamadı: ${error.message}`,
      };
    }

    if (!data) {
      return {
        success: false,
        error: 'Platform paketi bulunamadı.',
      };
    }

    // normalize single row as well
    const normalized: PlatformPackages = {
      ...data,
      price: typeof (data as any).price === 'string' ? Number((data as any).price) : (data.price as number),
    };

    return {
      success: true,
      data: normalized,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Bilinmeyen hata oluştu',
    };
  }
}
